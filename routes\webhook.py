from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import Response
from sqlalchemy.orm import Session
from models.database import get_db
from services.conversation_manager import ConversationManager
from services.whatsapp_service import WhatsAppService
from twilio.request_validator import RequestValidator
from config import settings
import logging
from typing import Optional

logger = logging.getLogger(__name__)

router = APIRouter()

def validate_twilio_request(request: Request, body: bytes) -> bool:
    """Validate that the request is from Twilio"""
    try:
        validator = RequestValidator(settings.twilio_auth_token)
        
        # Get the URL and signature
        url = str(request.url)
        signature = request.headers.get('X-Twilio-Signature', '')
        
        # Convert body to form data for validation
        form_data = {}
        if body:
            body_str = body.decode('utf-8')
            for pair in body_str.split('&'):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    form_data[key] = value
        
        return validator.validate(url, form_data, signature)
    except Exception as e:
        logger.error(f"Twilio validation error: {str(e)}")
        return False

@router.post("/whatsapp")
async def whatsapp_webhook(
    db: Session = Depends(get_db),
    From: str = Form(...),
    Body: str = Form(default=""),
    MediaUrl0: Optional[str] = Form(default=None),
    MessageSid: Optional[str] = Form(default=None),
    NumMedia: Optional[str] = Form(default="0")
):
    """Handle incoming WhatsApp messages"""
    try:
        # Validate request is from Twilio (optional, comment out for testing)
        # if not validate_twilio_request(request, body):
        #     raise HTTPException(status_code=403, detail="Invalid request signature")
        
        logger.info(f"Received WhatsApp message from {From}: {Body}")
        
        # Initialize conversation manager
        conversation_manager = ConversationManager(db)
        whatsapp_service = WhatsAppService()
        
        # Extract phone number (remove whatsapp: prefix)
        phone_number = From.replace('whatsapp:', '')
        
        # Handle media if present
        media_url = None
        if NumMedia and int(NumMedia) > 0 and MediaUrl0:
            media_url = MediaUrl0
            logger.info(f"Media received: {media_url}")
        
        # Process the message
        response_message = conversation_manager.process_message(
            phone_number=phone_number,
            message_content=Body,
            media_url=media_url,
            twilio_sid=MessageSid
        )
        
        # Create TwiML response
        twiml_response = whatsapp_service.create_response(response_message)
        
        return Response(
            content=str(twiml_response),
            media_type="application/xml"
        )
        
    except Exception as e:
        logger.error(f"Error processing WhatsApp webhook: {str(e)}")
        
        # Return a generic error message
        whatsapp_service = WhatsAppService()
        error_response = whatsapp_service.create_response(
            "I'm sorry, there was an error processing your request. Please try again later."
        )
        
        return Response(
            content=str(error_response),
            media_type="application/xml"
        )

@router.get("/whatsapp")
async def whatsapp_webhook_verification(request: Request):
    """Handle WhatsApp webhook verification (GET request)"""
    # This is used by some webhook services for verification
    return {"status": "WhatsApp webhook is active"}

@router.post("/send-message")
async def send_message(
    request: dict,
    db: Session = Depends(get_db)
):
    """Manual endpoint to send messages (for testing)"""
    try:
        phone_number = request.get("phone_number")
        message = request.get("message")
        
        if not phone_number or not message:
            raise HTTPException(status_code=400, detail="phone_number and message are required")
        
        whatsapp_service = WhatsAppService()
        success = whatsapp_service.send_message(phone_number, message)
        
        if success:
            return {"status": "success", "message": "Message sent successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to send message")
            
    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
