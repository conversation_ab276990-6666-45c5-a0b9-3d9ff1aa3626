from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from models.database import get_db
from models.user import User
from models.service import ServiceCategory, Service, Application, Document
from models.conversation import Conversation, Message
from typing import List, Optional
import json

router = APIRouter(prefix="/admin", tags=["admin"])

@router.get("/users")
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get list of users"""
    users = db.query(User).offset(skip).limit(limit).all()
    total = db.query(User).count()
    
    return {
        "users": users,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.get("/users/{user_id}")
async def get_user(user_id: int, db: Session = Depends(get_db)):
    """Get user details"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

@router.get("/applications")
async def get_applications(
    status: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get list of applications"""
    query = db.query(Application)
    
    if status:
        query = query.filter(Application.status == status)
    
    applications = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "applications": applications,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.get("/applications/{application_id}")
async def get_application(application_id: int, db: Session = Depends(get_db)):
    """Get application details with documents"""
    application = db.query(Application).filter(Application.id == application_id).first()
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    
    # Get associated documents
    documents = db.query(Document).filter(Document.application_id == application_id).all()
    
    return {
        "application": application,
        "documents": documents,
        "user": application.user,
        "service": application.service
    }

@router.put("/applications/{application_id}/status")
async def update_application_status(
    application_id: int,
    status_data: dict,
    db: Session = Depends(get_db)
):
    """Update application status"""
    application = db.query(Application).filter(Application.id == application_id).first()
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    
    new_status = status_data.get("status")
    if new_status not in ["pending", "submitted", "approved", "rejected"]:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    application.status = new_status
    db.commit()
    
    return {"message": "Status updated successfully", "application": application}

@router.get("/conversations")
async def get_conversations(
    user_id: Optional[int] = Query(None),
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get list of conversations"""
    query = db.query(Conversation)
    
    if user_id:
        query = query.filter(Conversation.user_id == user_id)
    
    if is_active is not None:
        query = query.filter(Conversation.is_active == is_active)
    
    conversations = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "conversations": conversations,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(
    conversation_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get messages for a conversation"""
    conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    
    messages = db.query(Message).filter(
        Message.conversation_id == conversation_id
    ).order_by(Message.created_at.desc()).offset(skip).limit(limit).all()
    
    total = db.query(Message).filter(Message.conversation_id == conversation_id).count()
    
    return {
        "conversation": conversation,
        "messages": messages,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.get("/services")
async def get_services(db: Session = Depends(get_db)):
    """Get all service categories and services"""
    categories = db.query(ServiceCategory).all()
    
    result = []
    for category in categories:
        services = db.query(Service).filter(Service.category_id == category.id).all()
        result.append({
            "category": category,
            "services": services
        })
    
    return result

@router.post("/services/load-from-config")
async def load_services_from_config(db: Session = Depends(get_db)):
    """Load services from JSON configuration file"""
    try:
        with open('data/services_config.json', 'r') as f:
            config = json.load(f)
        
        categories_data = config.get("service_categories", [])
        
        for cat_data in categories_data:
            # Create or update category
            category = db.query(ServiceCategory).filter(
                ServiceCategory.name == cat_data["name"]
            ).first()
            
            if not category:
                category = ServiceCategory(
                    name=cat_data["name"],
                    description=cat_data.get("description", "")
                )
                db.add(category)
                db.commit()
                db.refresh(category)
            
            # Create or update services
            for service_data in cat_data.get("services", []):
                service = db.query(Service).filter(
                    Service.category_id == category.id,
                    Service.name == service_data["name"]
                ).first()
                
                if not service:
                    service = Service(
                        category_id=category.id,
                        name=service_data["name"],
                        description=service_data.get("description", ""),
                        required_documents=service_data.get("required_documents", [])
                    )
                    db.add(service)
        
        db.commit()
        
        return {"message": "Services loaded successfully from configuration"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load services: {str(e)}")

@router.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get dashboard statistics"""
    from datetime import datetime, timedelta
    from sqlalchemy import func

    # Basic counts
    total_users = db.query(User).count()
    total_applications = db.query(Application).count()
    pending_applications = db.query(Application).filter(Application.status == "pending").count()
    submitted_applications = db.query(Application).filter(Application.status == "submitted").count()
    approved_applications = db.query(Application).filter(Application.status == "approved").count()
    rejected_applications = db.query(Application).filter(Application.status == "rejected").count()
    active_conversations = db.query(Conversation).filter(Conversation.is_active == True).count()

    # Time-based statistics
    today = datetime.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # New users this week/month
    new_users_week = db.query(User).filter(func.date(User.created_at) >= week_ago).count()
    new_users_month = db.query(User).filter(func.date(User.created_at) >= month_ago).count()

    # Applications this week/month
    new_applications_week = db.query(Application).filter(func.date(Application.created_at) >= week_ago).count()
    new_applications_month = db.query(Application).filter(func.date(Application.created_at) >= month_ago).count()

    # Most popular services
    popular_services = db.query(
        Service.name,
        func.count(Application.id).label('count')
    ).join(Application).group_by(Service.id, Service.name).order_by(func.count(Application.id).desc()).limit(5).all()

    return {
        "total_users": total_users,
        "total_applications": total_applications,
        "pending_applications": pending_applications,
        "submitted_applications": submitted_applications,
        "approved_applications": approved_applications,
        "rejected_applications": rejected_applications,
        "active_conversations": active_conversations,
        "new_users_week": new_users_week,
        "new_users_month": new_users_month,
        "new_applications_week": new_applications_week,
        "new_applications_month": new_applications_month,
        "popular_services": [{"name": name, "count": count} for name, count in popular_services]
    }

@router.post("/applications/{application_id}/notify")
async def notify_user(
    application_id: int,
    notification_data: dict,
    db: Session = Depends(get_db)
):
    """Send notification to user about application status"""
    application = db.query(Application).filter(Application.id == application_id).first()
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")

    user = application.user
    message = notification_data.get("message", "Your application status has been updated.")

    # Send WhatsApp notification
    from services.whatsapp_service import WhatsAppService
    whatsapp = WhatsAppService()

    success = whatsapp.send_message(f"+{user.phone_number}", message)

    if success:
        return {"message": "Notification sent successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to send notification")

@router.get("/analytics/daily")
async def get_daily_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get daily analytics for the specified number of days"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, text

    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days)

    # Daily user registrations
    daily_users = db.execute(text("""
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM users
        WHERE DATE(created_at) BETWEEN :start_date AND :end_date
        GROUP BY DATE(created_at)
        ORDER BY date
    """), {"start_date": start_date, "end_date": end_date}).fetchall()

    # Daily applications
    daily_applications = db.execute(text("""
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM applications
        WHERE DATE(created_at) BETWEEN :start_date AND :end_date
        GROUP BY DATE(created_at)
        ORDER BY date
    """), {"start_date": start_date, "end_date": end_date}).fetchall()

    return {
        "period": {"start": start_date, "end": end_date, "days": days},
        "daily_users": [{"date": str(row[0]), "count": row[1]} for row in daily_users],
        "daily_applications": [{"date": str(row[0]), "count": row[1]} for row in daily_applications]
    }

@router.get("/export/applications")
async def export_applications(
    format: str = Query("csv", regex="^(csv|json|excel)$"),
    status: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Export applications data"""
    from datetime import datetime
    import pandas as pd
    import io
    from fastapi.responses import StreamingResponse

    # Build query
    query = db.query(Application).join(User).join(Service)

    if status:
        query = query.filter(Application.status == status)

    if start_date:
        start = datetime.strptime(start_date, "%Y-%m-%d")
        query = query.filter(Application.created_at >= start)

    if end_date:
        end = datetime.strptime(end_date, "%Y-%m-%d")
        query = query.filter(Application.created_at <= end)

    applications = query.all()

    # Prepare data
    data = []
    for app in applications:
        data.append({
            "Application ID": app.id,
            "User Phone": app.user.phone_number,
            "User Name": app.user.name or "N/A",
            "Service": app.service.name if app.service else "N/A",
            "Status": app.status,
            "Created At": app.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "Updated At": app.updated_at.strftime("%Y-%m-%d %H:%M:%S") if app.updated_at else "N/A"
        })

    df = pd.DataFrame(data)

    if format == "csv":
        output = io.StringIO()
        df.to_csv(output, index=False)
        output.seek(0)

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode()),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=applications.csv"}
        )

    elif format == "json":
        return {"applications": data}

    elif format == "excel":
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Applications', index=False)
        output.seek(0)

        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=applications.xlsx"}
        )

@router.post("/bulk-update")
async def bulk_update_applications(
    update_data: dict,
    db: Session = Depends(get_db)
):
    """Bulk update application statuses"""
    application_ids = update_data.get("application_ids", [])
    new_status = update_data.get("status")

    if not application_ids or not new_status:
        raise HTTPException(status_code=400, detail="application_ids and status are required")

    if new_status not in ["pending", "submitted", "approved", "rejected"]:
        raise HTTPException(status_code=400, detail="Invalid status")

    # Update applications
    updated_count = db.query(Application).filter(
        Application.id.in_(application_ids)
    ).update({"status": new_status}, synchronize_session=False)

    db.commit()

    return {
        "message": f"Updated {updated_count} applications",
        "updated_count": updated_count,
        "new_status": new_status
    }
