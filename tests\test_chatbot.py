import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os

# Import application components
from main import app
from models.database import Base, get_db
from models.user import User
from models.service import ServiceCategory, Service, Application
from models.conversation import Conversation, Message
from services.conversation_manager import ConversationManager
from services.whatsapp_service import WhatsAppService

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def setup_database():
    """Setup test database"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)

@pytest.fixture
def db_session():
    """Create database session for testing"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture
def sample_user(db_session):
    """Create a sample user for testing"""
    user = User(phone_number="**********", name="Test User")
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def sample_service(db_session):
    """Create sample service category and service"""
    category = ServiceCategory(name="Test Category", description="Test Description")
    db_session.add(category)
    db_session.commit()
    db_session.refresh(category)
    
    service = Service(
        category_id=category.id,
        name="Test Service",
        description="Test Service Description",
        required_documents=["Document 1", "Document 2"]
    )
    db_session.add(service)
    db_session.commit()
    db_session.refresh(service)
    
    return category, service

class TestWebhookEndpoints:
    """Test webhook endpoints"""
    
    def test_health_check(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_whatsapp_webhook_start(self, client, setup_database):
        """Test WhatsApp webhook with start message"""
        response = client.post("/webhook/whatsapp", data={
            "From": "whatsapp:+**********",
            "Body": "start",
            "MessageSid": "test123",
            "NumMedia": "0"
        })
        assert response.status_code == 200
        assert "Hello!" in response.text
        assert "service category" in response.text
    
    def test_whatsapp_webhook_invalid_data(self, client):
        """Test webhook with invalid data"""
        response = client.post("/webhook/whatsapp", data={
            "Body": "test"
            # Missing required From field
        })
        assert response.status_code == 422  # Validation error

class TestConversationManager:
    """Test conversation management logic"""
    
    def test_get_or_create_user(self, db_session):
        """Test user creation and retrieval"""
        manager = ConversationManager(db_session)
        
        # Test creating new user
        user1 = manager.get_or_create_user("whatsapp:+**********")
        assert user1.phone_number == "**********"
        
        # Test retrieving existing user
        user2 = manager.get_or_create_user("+**********")
        assert user1.id == user2.id
    
    def test_conversation_creation(self, db_session, sample_user):
        """Test conversation creation"""
        manager = ConversationManager(db_session)
        
        conversation = manager.get_or_create_conversation(sample_user)
        assert conversation.user_id == sample_user.id
        assert conversation.current_step == "greeting"
        assert conversation.is_active == True
    
    def test_greeting_flow(self, db_session, sample_user):
        """Test greeting message flow"""
        manager = ConversationManager(db_session)
        conversation = manager.get_or_create_conversation(sample_user)
        
        response = manager.handle_greeting(conversation)
        assert "Hello!" in response
        assert "service category" in response
        assert conversation.current_step == "category_selection"

class TestWhatsAppService:
    """Test WhatsApp service functionality"""
    
    def test_format_service_list(self):
        """Test service list formatting"""
        whatsapp = WhatsAppService()
        categories = [
            {"name": "Medical", "description": "Medical services"},
            {"name": "Legal", "description": "Legal services"}
        ]
        
        result = whatsapp.format_service_list(categories)
        assert "1. Medical" in result
        assert "2. Legal" in result
        assert "reply with the number" in result
    
    def test_format_document_requirements(self):
        """Test document requirements formatting"""
        whatsapp = WhatsAppService()
        documents = ["ID Proof", "Address Proof", "Photo"]
        
        result = whatsapp.format_document_requirements("Test Service", documents)
        assert "Test Service" in result
        assert "1. ID Proof" in result
        assert "2. Address Proof" in result
        assert "3. Photo" in result
        assert "upload" in result.lower()

class TestAdminEndpoints:
    """Test admin API endpoints"""
    
    def test_admin_stats(self, client, setup_database):
        """Test admin statistics endpoint"""
        response = client.get("/admin/stats")
        assert response.status_code == 200
        data = response.json()
        assert "total_users" in data
        assert "total_applications" in data
        assert "active_conversations" in data
    
    def test_admin_users_list(self, client, setup_database, sample_user):
        """Test admin users list endpoint"""
        response = client.get("/admin/users")
        assert response.status_code == 200
        data = response.json()
        assert "users" in data
        assert "total" in data
        assert data["total"] >= 1
    
    def test_admin_services_list(self, client, setup_database, sample_service):
        """Test admin services list endpoint"""
        response = client.get("/admin/services")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

class TestDocumentValidation:
    """Test document upload and validation"""
    
    def test_file_extension_validation(self):
        """Test file extension validation"""
        from services.document_service import DocumentService
        
        db_session = TestingSessionLocal()
        doc_service = DocumentService(db_session)
        
        # Test valid extensions
        assert doc_service._is_allowed_file("document.pdf") == True
        assert doc_service._is_allowed_file("image.jpg") == True
        assert doc_service._is_allowed_file("image.png") == True
        
        # Test invalid extensions
        assert doc_service._is_allowed_file("script.exe") == False
        assert doc_service._is_allowed_file("data.txt") == False
    
    def test_file_content_validation(self):
        """Test file content validation"""
        from services.document_service import DocumentService
        
        db_session = TestingSessionLocal()
        doc_service = DocumentService(db_session)
        
        # Test PDF signature
        pdf_content = b'%PDF-1.4\n%\xe2\xe3\xcf\xd3\n'
        is_valid, message = doc_service._validate_file_content(pdf_content, "test.pdf")
        assert is_valid == True
        
        # Test invalid content
        invalid_content = b'invalid content'
        is_valid, message = doc_service._validate_file_content(invalid_content, "test.pdf")
        assert is_valid == False

class TestCompleteConversationFlow:
    """Test complete conversation flow from start to finish"""
    
    def test_full_conversation_flow(self, client, setup_database):
        """Test complete conversation from greeting to submission"""
        phone_number = "whatsapp:+9876543210"
        
        # Step 1: Start conversation
        response = client.post("/webhook/whatsapp", data={
            "From": phone_number,
            "Body": "start",
            "MessageSid": "test1",
            "NumMedia": "0"
        })
        assert response.status_code == 200
        assert "Hello!" in response.text
        
        # Step 2: Select category (assuming category 1 exists)
        response = client.post("/webhook/whatsapp", data={
            "From": phone_number,
            "Body": "1",
            "MessageSid": "test2",
            "NumMedia": "0"
        })
        assert response.status_code == 200
        
        # Step 3: Select service (assuming service 1 exists)
        response = client.post("/webhook/whatsapp", data={
            "From": phone_number,
            "Body": "1",
            "MessageSid": "test3",
            "NumMedia": "0"
        })
        assert response.status_code == 200
        
        # Verify user was created
        db = TestingSessionLocal()
        user = db.query(User).filter(User.phone_number == "9876543210").first()
        assert user is not None
        
        # Verify conversation was created
        conversation = db.query(Conversation).filter(Conversation.user_id == user.id).first()
        assert conversation is not None
        assert conversation.is_active == True
        
        db.close()

class TestErrorHandling:
    """Test error handling scenarios"""
    
    def test_invalid_category_selection(self, client, setup_database):
        """Test invalid category selection"""
        response = client.post("/webhook/whatsapp", data={
            "From": "whatsapp:+1111111111",
            "Body": "start",
            "MessageSid": "test1",
            "NumMedia": "0"
        })
        
        # Try invalid category number
        response = client.post("/webhook/whatsapp", data={
            "From": "whatsapp:+1111111111",
            "Body": "999",
            "MessageSid": "test2",
            "NumMedia": "0"
        })
        assert response.status_code == 200
        assert "Invalid choice" in response.text
    
    def test_non_numeric_input(self, client, setup_database):
        """Test non-numeric input for category selection"""
        response = client.post("/webhook/whatsapp", data={
            "From": "whatsapp:+2222222222",
            "Body": "start",
            "MessageSid": "test1",
            "NumMedia": "0"
        })
        
        # Try non-numeric input
        response = client.post("/webhook/whatsapp", data={
            "From": "whatsapp:+2222222222",
            "Body": "invalid",
            "MessageSid": "test2",
            "NumMedia": "0"
        })
        assert response.status_code == 200
        assert "valid number" in response.text

# Performance tests
class TestPerformance:
    """Test performance scenarios"""
    
    def test_concurrent_users(self, client, setup_database):
        """Test handling multiple concurrent users"""
        import threading
        import time
        
        results = []
        
        def simulate_user(user_id):
            response = client.post("/webhook/whatsapp", data={
                "From": f"whatsapp:+{user_id}",
                "Body": "start",
                "MessageSid": f"test_{user_id}",
                "NumMedia": "0"
            })
            results.append(response.status_code)
        
        # Simulate 10 concurrent users
        threads = []
        for i in range(10):
            thread = threading.Thread(target=simulate_user, args=(f"555000{i:03d}",))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 10

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
