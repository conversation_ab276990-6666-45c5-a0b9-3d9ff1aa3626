import re
import os
import json
from datetime import datetime
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

def clean_phone_number(phone_number: str) -> str:
    """Clean and format phone number"""
    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone_number)
    
    # Remove whatsapp: prefix if present
    if cleaned.startswith('whatsapp:'):
        cleaned = cleaned.replace('whatsapp:', '')
    
    # Remove + if present
    if cleaned.startswith('+'):
        cleaned = cleaned[1:]
    
    return cleaned

def format_phone_for_whatsapp(phone_number: str) -> str:
    """Format phone number for WhatsApp API"""
    cleaned = clean_phone_number(phone_number)
    return f"whatsapp:+{cleaned}"

def validate_phone_number(phone_number: str) -> bool:
    """Validate phone number format"""
    cleaned = clean_phone_number(phone_number)
    # Basic validation: should be 10-15 digits
    return len(cleaned) >= 10 and len(cleaned) <= 15 and cleaned.isdigit()

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    # Remove or replace unsafe characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

def get_file_size_mb(file_size_bytes: int) -> float:
    """Convert file size from bytes to MB"""
    return round(file_size_bytes / (1024 * 1024), 2)

def is_image_file(filename: str) -> bool:
    """Check if file is an image"""
    image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    extension = filename.split('.')[-1].lower() if '.' in filename else ''
    return extension in image_extensions

def is_document_file(filename: str) -> bool:
    """Check if file is a document"""
    doc_extensions = ['pdf', 'doc', 'docx', 'txt', 'rtf']
    extension = filename.split('.')[-1].lower() if '.' in filename else ''
    return extension in doc_extensions

def format_datetime(dt: datetime) -> str:
    """Format datetime for display"""
    if not dt:
        return ""
    return dt.strftime("%Y-%m-%d %H:%M:%S")

def format_date(dt: datetime) -> str:
    """Format date for display"""
    if not dt:
        return ""
    return dt.strftime("%Y-%m-%d")

def load_json_file(file_path: str) -> Optional[Dict[Any, Any]]:
    """Load JSON file safely"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load JSON file {file_path}: {str(e)}")
        return None

def save_json_file(data: Dict[Any, Any], file_path: str) -> bool:
    """Save data to JSON file safely"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        logger.error(f"Failed to save JSON file {file_path}: {str(e)}")
        return False

def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def extract_number_from_text(text: str) -> Optional[int]:
    """Extract first number from text"""
    numbers = re.findall(r'\d+', text)
    if numbers:
        try:
            return int(numbers[0])
        except ValueError:
            pass
    return None

def create_directory_if_not_exists(directory_path: str) -> bool:
    """Create directory if it doesn't exist"""
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {directory_path}: {str(e)}")
        return False

def get_file_extension(filename: str) -> str:
    """Get file extension from filename"""
    return filename.split('.')[-1].lower() if '.' in filename else ''

def generate_application_reference(application_id: int) -> str:
    """Generate application reference number"""
    timestamp = datetime.now().strftime("%Y%m%d")
    return f"APP-{timestamp}-{application_id:06d}"

def mask_phone_number(phone_number: str) -> str:
    """Mask phone number for display (show only last 4 digits)"""
    if len(phone_number) <= 4:
        return phone_number
    return "*" * (len(phone_number) - 4) + phone_number[-4:]

def validate_file_type(filename: str, allowed_extensions: list) -> bool:
    """Validate if file type is allowed"""
    extension = get_file_extension(filename)
    return extension in [ext.lower() for ext in allowed_extensions]
