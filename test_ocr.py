#!/usr/bin/env python3
"""
Test OCR functionality
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ocr_dependencies():
    """Test if OCR dependencies are available"""
    print("🔍 TESTING OCR DEPENDENCIES")
    print("=" * 40)
    
    # Test 1: Check if pytesseract is installed
    try:
        import pytesseract
        print("✅ pytesseract: Installed")
    except ImportError:
        print("❌ pytesseract: Not installed")
        return False
    
    # Test 2: Check if opencv is installed
    try:
        import cv2
        print("✅ opencv-python: Installed")
    except ImportError:
        print("❌ opencv-python: Not installed")
        return False
    
    # Test 3: Check if PIL is installed
    try:
        from PIL import Image
        print("✅ Pillow (PIL): Installed")
    except ImportError:
        print("❌ Pillow (PIL): Not installed")
        return False
    
    # Test 4: Check if pdf2image is installed
    try:
        from pdf2image import convert_from_path
        print("✅ pdf2image: Installed")
    except ImportError:
        print("❌ pdf2image: Not installed")
        return False
    
    # Test 5: Check Tesseract executable
    tesseract_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        "tesseract"
    ]
    
    tesseract_found = False
    for path in tesseract_paths:
        if os.path.exists(path) or os.system(f"where {path} >nul 2>&1") == 0:
            print(f"✅ Tesseract executable: Found at {path}")
            tesseract_found = True
            break
    
    if not tesseract_found:
        print("⚠️ Tesseract executable: Not found in common locations")
        print("   Download from: https://github.com/UB-Mannheim/tesseract/wiki")
    
    return True

def test_ocr_service():
    """Test OCR service functionality"""
    print("\n🔍 TESTING OCR SERVICE")
    print("=" * 40)
    
    try:
        from services.ocr_service import OCRService
        
        # Initialize OCR service
        ocr_service = OCRService()
        print("✅ OCR Service: Initialized successfully")
        
        # Test image creation and OCR
        from PIL import Image, ImageDraw, ImageFont
        import tempfile
        
        # Create a test image with text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add text to image
        text = "Test Document\nGovernment of India\nBirth Certificate\nName: John Doe\nDate: 01/01/2000"
        draw.text((20, 20), text, fill='black')
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            img.save(temp_file.name)
            temp_path = temp_file.name
        
        print(f"✅ Test image created: {temp_path}")
        
        # Test OCR processing
        result = ocr_service.process_document(temp_path, "test_document", 999)
        
        if result.get('success'):
            print("✅ OCR Processing: Successful")
            print(f"📝 Extracted text length: {len(result.get('full_text', ''))}")
            print(f"📊 Confidence: {result.get('average_confidence', 0):.1f}%")
            print(f"📄 Text blocks: {result.get('total_blocks', 0)}")
            
            # Show first 100 characters of extracted text
            extracted_text = result.get('full_text', '')
            if extracted_text:
                print(f"📖 Sample text: {extracted_text[:100]}...")
            
            # Check if JSON file was created
            json_path = result.get('json_path')
            if json_path and os.path.exists(json_path):
                print(f"✅ JSON file created: {json_path}")
            else:
                print("⚠️ JSON file not created")
        else:
            print(f"❌ OCR Processing failed: {result.get('error', 'Unknown error')}")
        
        # Clean up
        os.unlink(temp_path)
        print("✅ Test cleanup completed")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ OCR Service test failed: {str(e)}")
        return False

def test_upload_directory():
    """Test upload directory functionality"""
    print("\n📁 TESTING UPLOAD DIRECTORY")
    print("=" * 40)
    
    from config import settings
    
    upload_dir = Path(settings.upload_dir)
    print(f"📂 Upload directory: {upload_dir}")
    
    # Test 1: Directory exists
    if upload_dir.exists():
        print("✅ Directory exists")
    else:
        print("❌ Directory does not exist")
        try:
            upload_dir.mkdir(parents=True, exist_ok=True)
            print("✅ Directory created successfully")
        except Exception as e:
            print(f"❌ Failed to create directory: {e}")
            return False
    
    # Test 2: Directory is writable
    try:
        test_file = upload_dir / "test_write.txt"
        test_file.write_text("test")
        test_file.unlink()
        print("✅ Directory is writable")
    except Exception as e:
        print(f"❌ Directory is not writable: {e}")
        return False
    
    # Test 3: Check OCR output directory
    ocr_dir = Path(settings.ocr_output_dir)
    if ocr_dir.exists():
        print(f"✅ OCR output directory exists: {ocr_dir}")
    else:
        print(f"⚠️ OCR output directory does not exist: {ocr_dir}")
    
    return True

def main():
    """Main test function"""
    print("🧪 WHATSAPP OCR CHATBOT - OCR TESTING")
    print("=" * 50)
    
    # Test 1: Dependencies
    deps_ok = test_ocr_dependencies()
    
    # Test 2: Upload directory
    dir_ok = test_upload_directory()
    
    # Test 3: OCR service (only if dependencies are OK)
    ocr_ok = False
    if deps_ok:
        ocr_ok = test_ocr_service()
    else:
        print("\n⚠️ Skipping OCR service test due to missing dependencies")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 OCR TESTING SUMMARY")
    print("=" * 50)
    print(f"📦 Dependencies: {'✅ PASS' if deps_ok else '❌ FAIL'}")
    print(f"📁 Upload Directory: {'✅ PASS' if dir_ok else '❌ FAIL'}")
    print(f"🔍 OCR Service: {'✅ PASS' if ocr_ok else '❌ FAIL' if deps_ok else '⏭️ SKIP'}")
    
    if deps_ok and dir_ok and ocr_ok:
        print("\n🎉 ALL OCR TESTS PASSED!")
        print("✅ OCR functionality is ready to use")
    elif not deps_ok:
        print("\n⚠️ INSTALL MISSING DEPENDENCIES")
        print("Run: pip install pytesseract opencv-python pdf2image")
        print("Install Tesseract: https://github.com/UB-Mannheim/tesseract/wiki")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Check the error messages above")

if __name__ == "__main__":
    main()
