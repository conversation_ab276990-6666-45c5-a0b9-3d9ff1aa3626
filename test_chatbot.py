#!/usr/bin/env python3
"""
Test script for WhatsApp Government Services Chatbot
This script simulates WhatsApp webhook calls to test the chatbot functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_webhook(phone_number, message, media_url=None):
    """Simulate a WhatsApp webhook call"""
    url = f"{BASE_URL}/webhook/whatsapp"
    
    data = {
        "From": f"whatsapp:{phone_number}",
        "Body": message,
        "MessageSid": f"test_msg_{int(time.time())}",
        "NumMedia": "1" if media_url else "0"
    }
    
    if media_url:
        data["MediaUrl0"] = media_url
    
    try:
        response = requests.post(url, data=data)
        print(f"📱 User ({phone_number}): {message}")
        if response.status_code == 200:
            # Parse TwiML response
            response_text = response.text
            if "<Message>" in response_text:
                # Extract message content from TwiML
                start = response_text.find("<Message>") + 9
                end = response_text.find("</Message>")
                bot_response = response_text[start:end].strip()
                print(f"🤖 Bot: {bot_response}")
            else:
                print(f"🤖 Bot: {response_text}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
        print("-" * 50)
        return response
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_admin_endpoints():
    """Test admin endpoints"""
    print("🔧 Testing Admin Endpoints")
    print("=" * 50)
    
    # Test stats
    try:
        response = requests.get(f"{BASE_URL}/admin/stats")
        if response.status_code == 200:
            stats = response.json()
            print("📊 Dashboard Stats:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        else:
            print(f"❌ Stats Error: {response.status_code}")
    except Exception as e:
        print(f"❌ Stats Error: {str(e)}")
    
    print("-" * 50)
    
    # Test services
    try:
        response = requests.get(f"{BASE_URL}/admin/services")
        if response.status_code == 200:
            services = response.json()
            print("🏢 Available Services:")
            for category_data in services:
                category = category_data["category"]
                print(f"  📂 {category['name']}: {category['description']}")
                for service in category_data["services"]:
                    print(f"    📄 {service['name']}")
                    print(f"       Required docs: {', '.join(service['required_documents'])}")
        else:
            print(f"❌ Services Error: {response.status_code}")
    except Exception as e:
        print(f"❌ Services Error: {str(e)}")
    
    print("=" * 50)

def simulate_chatbot_conversation():
    """Simulate a complete chatbot conversation"""
    print("🤖 Simulating WhatsApp Chatbot Conversation")
    print("=" * 50)
    
    phone_number = "+1234567890"
    
    # Step 1: Start conversation
    test_webhook(phone_number, "start")
    time.sleep(1)
    
    # Step 2: Select service category (Civil Registration)
    test_webhook(phone_number, "3")
    time.sleep(1)
    
    # Step 3: Select specific service (Birth Certificate)
    test_webhook(phone_number, "1")
    time.sleep(1)
    
    # Step 4: Upload documents (simulate)
    print("📎 Simulating document uploads...")
    test_webhook(phone_number, "Uploading proof of birth", "https://example.com/document1.pdf")
    time.sleep(1)
    
    test_webhook(phone_number, "Uploading identity proof", "https://example.com/document2.pdf")
    time.sleep(1)
    
    test_webhook(phone_number, "Uploading hospital discharge", "https://example.com/document3.pdf")
    time.sleep(1)
    
    # Step 5: Confirm submission
    test_webhook(phone_number, "yes")
    time.sleep(1)
    
    print("✅ Conversation simulation completed!")

def main():
    """Main test function"""
    print("🚀 WhatsApp Government Services Chatbot - Test Suite")
    print("=" * 60)
    
    # Test if server is running
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server health check failed")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {str(e)}")
        print("Make sure the server is running with: python main.py")
        return
    
    print()
    
    # Test admin endpoints
    test_admin_endpoints()
    print()
    
    # Simulate chatbot conversation
    simulate_chatbot_conversation()
    print()
    
    # Final stats check
    print("📊 Final Statistics:")
    try:
        response = requests.get(f"{BASE_URL}/admin/stats")
        if response.status_code == 200:
            stats = response.json()
            for key, value in stats.items():
                print(f"  {key}: {value}")
    except Exception as e:
        print(f"❌ Error getting final stats: {str(e)}")

if __name__ == "__main__":
    main()
