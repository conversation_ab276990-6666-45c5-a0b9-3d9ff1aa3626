#!/usr/bin/env python3
"""
Setup script for WhatsApp Government Services Chatbot
"""

import os
import sys
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import Base, create_tables
from models.user import User
from models.service import ServiceCategory, Service, Application, Document
from models.conversation import Conversation, Message
from config import settings

def setup_database():
    """Create database tables"""
    print("Creating database tables...")
    create_tables()
    print("✅ Database tables created successfully")

def load_initial_services():
    """Load initial services from configuration"""
    print("Loading initial services...")
    
    try:
        # Create database session
        engine = create_engine(settings.database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Load services configuration
        with open('data/services_config.json', 'r') as f:
            config = json.load(f)
        
        categories_data = config.get("service_categories", [])
        
        for cat_data in categories_data:
            # Check if category already exists
            existing_category = db.query(ServiceCategory).filter(
                ServiceCategory.name == cat_data["name"]
            ).first()
            
            if not existing_category:
                # Create new category
                category = ServiceCategory(
                    name=cat_data["name"],
                    description=cat_data.get("description", "")
                )
                db.add(category)
                db.commit()
                db.refresh(category)
                print(f"  ✅ Created category: {category.name}")
            else:
                category = existing_category
                print(f"  ⏭️  Category already exists: {category.name}")
            
            # Create services
            for service_data in cat_data.get("services", []):
                existing_service = db.query(Service).filter(
                    Service.category_id == category.id,
                    Service.name == service_data["name"]
                ).first()
                
                if not existing_service:
                    service = Service(
                        category_id=category.id,
                        name=service_data["name"],
                        description=service_data.get("description", ""),
                        required_documents=service_data.get("required_documents", [])
                    )
                    db.add(service)
                    print(f"    ✅ Created service: {service.name}")
        
        db.commit()
        db.close()
        print("✅ Initial services loaded successfully")
        
    except Exception as e:
        print(f"❌ Error loading services: {str(e)}")
        return False
    
    return True

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    if not os.path.exists('.env'):
        print("Creating .env file from template...")
        try:
            with open('.env.example', 'r') as template:
                content = template.read()
            
            with open('.env', 'w') as env_file:
                env_file.write(content)
            
            print("✅ .env file created from template")
            print("⚠️  Please update .env file with your actual configuration values")
        except Exception as e:
            print(f"❌ Error creating .env file: {str(e)}")
    else:
        print("⏭️  .env file already exists")

def create_upload_directory():
    """Create upload directory"""
    upload_dir = settings.upload_dir
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir, exist_ok=True)
        print(f"✅ Created upload directory: {upload_dir}")
    else:
        print(f"⏭️  Upload directory already exists: {upload_dir}")

def main():
    """Main setup function"""
    print("🚀 Setting up WhatsApp Government Services Chatbot...")
    print("=" * 50)
    
    # Create .env file
    create_env_file()
    
    # Create upload directory
    create_upload_directory()
    
    # Setup database
    setup_database()
    
    # Load initial services
    load_initial_services()
    
    print("=" * 50)
    print("✅ Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Update .env file with your Twilio credentials")
    print("2. Run: python -m pip install -r requirements.txt")
    print("3. Run: python main.py")
    print("4. Configure Twilio webhook URL to: http://your-domain/webhook/whatsapp")
    print("\n🎉 Your chatbot is ready to use!")

if __name__ == "__main__":
    main()
