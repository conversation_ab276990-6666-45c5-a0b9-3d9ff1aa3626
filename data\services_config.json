{"service_categories": [{"name": "Medical", "description": "Medical related services and certificates", "services": [{"name": "Medical Certificate", "description": "General medical certificate", "required_documents": ["Medical Report", "Identity Proof", "Passport Photo"]}, {"name": "Fitness Certificate", "description": "Physical fitness certificate", "required_documents": ["Medical Examination Report", "Identity Proof", "Passport Photo"]}]}, {"name": "Town Planning", "description": "Town planning and construction related services", "services": [{"name": "Building Permission", "description": "Permission for new construction", "required_documents": ["Site Plan", "Property Documents", "Identity Proof", "NOC from Neighbors"]}, {"name": "Completion Certificate", "description": "Certificate of construction completion", "required_documents": ["Building Permission Copy", "Completion Photos", "Engineer Certificate", "Property Documents"]}]}, {"name": "Civil Registration", "description": "Birth, death, and marriage certificates", "services": [{"name": "Birth Certificate", "description": "Original birth certificate", "required_documents": ["Proof of Birth", "Identity Proof of Parents", "Hospital Discharge Summary"]}, {"name": "Duplicate Birth Certificate", "description": "Duplicate copy of birth certificate", "required_documents": ["Original Birth Certificate", "Identity Proof", "Affidavit for Lost Certificate"]}, {"name": "Minor Corrections", "description": "Minor corrections in birth certificate", "required_documents": ["Original Birth Certificate", "Proof of Correction Required", "Identity Proof of Parents", "Affidavit for Correction"]}]}, {"name": "Revenue", "description": "Revenue and land related services", "services": [{"name": "Income Certificate", "description": "Certificate of annual income", "required_documents": ["Salary Certificate", "Bank Statements", "Identity Proof", "Address Proof"]}, {"name": "Caste Certificate", "description": "Certificate of caste/community", "required_documents": ["Community Certificate of Parents", "Identity Proof", "Address Proof", "School Certificate"]}]}]}