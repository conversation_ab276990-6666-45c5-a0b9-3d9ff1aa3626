#!/usr/bin/env python3
"""
Test script for WhatsApp OCR Chatbot conversation flow
"""

import requests
import json
import time

def test_conversation_flow():
    """Test complete conversation flow"""
    base_url = "http://localhost:8000"
    phone_number = "8983987726"
    
    print("🤖 TESTING WHATSAPP OCR CHATBOT CONVERSATION FLOW")
    print("=" * 60)
    
    def send_webhook_message(message, media_url=None):
        """Send message via webhook simulation"""
        data = {
            "from": f"whatsapp:{phone_number}",
            "body": message,
            "id": f"test_{int(time.time())}",
            "media": media_url or ""
        }
        
        response = requests.post(f"{base_url}/webhook/whatsapp-api", json=data)
        print(f"📱 User: {message}")
        print(f"📤 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status: {result.get('status', 'unknown')}")
        else:
            print(f"❌ Error: {response.text}")
        
        print("-" * 50)
        return response
    
    def test_endpoint(endpoint, method="GET", data=None):
        """Test API endpoint"""
        url = f"{base_url}{endpoint}"
        
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        
        print(f"🔗 Testing: {endpoint}")
        print(f"📤 Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ Response: {str(result)[:100]}...")
            except:
                print(f"✅ Response: {response.text[:100]}...")
        else:
            print(f"❌ Error: {response.text}")
        
        print("-" * 50)
        return response
    
    # Test 1: Health Check
    print("🏥 STEP 1: Health Check")
    test_endpoint("/health")
    
    # Test 2: WhatsApp Status
    print("📱 STEP 2: WhatsApp Status")
    test_endpoint("/webhook/whatsapp-status")
    
    # Test 3: Admin Stats
    print("📊 STEP 3: Admin Statistics")
    test_endpoint("/admin/stats")
    
    # Test 4: Available Services
    print("🏢 STEP 4: Available Services")
    test_endpoint("/admin/services")
    
    # Test 5: Conversation Flow
    print("💬 STEP 5: Conversation Flow Testing")
    
    # Step 5.1: Start conversation
    print("5.1: Starting conversation...")
    send_webhook_message("start")
    time.sleep(1)
    
    # Step 5.2: Select category (Civil Registration)
    print("5.2: Selecting Civil Registration...")
    send_webhook_message("3")
    time.sleep(1)
    
    # Step 5.3: Select service (Birth Certificate)
    print("5.3: Selecting Birth Certificate...")
    send_webhook_message("1")
    time.sleep(1)
    
    # Step 5.4: Upload documents (simulate)
    print("5.4: Simulating document uploads...")
    send_webhook_message("Uploading proof of birth", "https://example.com/birth_proof.pdf")
    time.sleep(1)
    
    send_webhook_message("Uploading identity proof", "https://example.com/id_proof.pdf")
    time.sleep(1)
    
    send_webhook_message("Uploading hospital discharge", "https://example.com/hospital.pdf")
    time.sleep(1)
    
    # Step 5.5: Confirm submission
    print("5.5: Confirming submission...")
    send_webhook_message("yes")
    time.sleep(1)
    
    # Test 6: Final Statistics
    print("📈 STEP 6: Final Statistics Check")
    response = test_endpoint("/admin/stats")
    
    if response.status_code == 200:
        stats = response.json()
        print(f"👥 Total Users: {stats.get('total_users', 0)}")
        print(f"📋 Total Applications: {stats.get('total_applications', 0)}")
        print(f"💬 Active Conversations: {stats.get('active_conversations', 0)}")
    
    print("\n" + "=" * 60)
    print("🎉 CONVERSATION FLOW TEST COMPLETED!")
    print("✅ All steps executed successfully")
    print(f"📱 Phone Number: {phone_number}")
    print("🔍 Check the admin dashboard for detailed results")

if __name__ == "__main__":
    test_conversation_flow()
