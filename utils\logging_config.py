import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path

def setup_logging(log_level="INFO", log_dir="logs"):
    """Setup comprehensive logging configuration"""
    
    # Create logs directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler for all logs
    file_handler = logging.handlers.RotatingFileHandler(
        log_path / "chatbot.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        log_path / "errors.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # WhatsApp specific logger
    whatsapp_logger = logging.getLogger("whatsapp")
    whatsapp_handler = logging.handlers.RotatingFileHandler(
        log_path / "whatsapp.log",
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3
    )
    whatsapp_handler.setLevel(logging.INFO)
    whatsapp_handler.setFormatter(detailed_formatter)
    whatsapp_logger.addHandler(whatsapp_handler)
    whatsapp_logger.propagate = False
    
    # Database logger
    db_logger = logging.getLogger("database")
    db_handler = logging.handlers.RotatingFileHandler(
        log_path / "database.log",
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3
    )
    db_handler.setLevel(logging.INFO)
    db_handler.setFormatter(detailed_formatter)
    db_logger.addHandler(db_handler)
    db_logger.propagate = False
    
    # Performance logger
    perf_logger = logging.getLogger("performance")
    perf_handler = logging.handlers.RotatingFileHandler(
        log_path / "performance.log",
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3
    )
    perf_handler.setLevel(logging.INFO)
    perf_handler.setFormatter(detailed_formatter)
    perf_logger.addHandler(perf_handler)
    perf_logger.propagate = False
    
    logging.info("Logging system initialized")

class PerformanceLogger:
    """Context manager for performance logging"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.logger = logging.getLogger("performance")
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(f"Started: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        if exc_type:
            self.logger.error(f"Failed: {self.operation_name} - Duration: {duration:.3f}s - Error: {exc_val}")
        else:
            self.logger.info(f"Completed: {self.operation_name} - Duration: {duration:.3f}s")

def log_whatsapp_message(phone_number: str, message_type: str, content: str, success: bool = True):
    """Log WhatsApp message activity"""
    logger = logging.getLogger("whatsapp")
    status = "SUCCESS" if success else "FAILED"
    logger.info(f"{status} - {message_type} - {phone_number} - {content[:100]}...")

def log_database_operation(operation: str, table: str, record_id: str = None, success: bool = True):
    """Log database operations"""
    logger = logging.getLogger("database")
    status = "SUCCESS" if success else "FAILED"
    record_info = f" - ID: {record_id}" if record_id else ""
    logger.info(f"{status} - {operation} - {table}{record_info}")

# Middleware for request logging
class RequestLoggingMiddleware:
    def __init__(self, app):
        self.app = app
        self.logger = logging.getLogger("requests")
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            start_time = datetime.now()
            
            # Log request
            method = scope["method"]
            path = scope["path"]
            client = scope.get("client", ["unknown", 0])
            self.logger.info(f"Request: {method} {path} from {client[0]}")
            
            # Process request
            await self.app(scope, receive, send)
            
            # Log response time
            duration = (datetime.now() - start_time).total_seconds()
            self.logger.info(f"Response: {method} {path} - Duration: {duration:.3f}s")
        else:
            await self.app(scope, receive, send)
