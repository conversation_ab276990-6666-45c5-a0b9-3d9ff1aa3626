# WhatsApp Government Services Chatbot

A comprehensive chatbot solution for government services via WhatsApp, built with FastAPI and Twilio.

## 🌟 Features

- **Multi-step Conversation Flow**: Guided service selection and document collection
- **Service Categories**: Medical, Town Planning, Civil Registration, Revenue services
- **Document Management**: Upload and store documents with validation
- **User Management**: Phone number-based user identification
- **Admin Dashboard**: Monitor applications, users, and conversations
- **WhatsApp Integration**: Seamless integration with Twilio WhatsApp API
- **File Upload Support**: Images (JPG, PNG) and documents (PDF, DOC, DOCX)

## 🏗️ Architecture

```
├── main.py                 # FastAPI application entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── setup.py              # Setup script
├── models/               # Database models
│   ├── database.py       # Database connection
│   ├── user.py          # User model
│   ├── service.py       # Service and application models
│   └── conversation.py  # Conversation and message models
├── routes/              # API routes
│   ├── webhook.py       # WhatsApp webhook handler
│   └── admin.py         # Admin endpoints
├── services/            # Business logic
│   ├── whatsapp_service.py      # WhatsApp messaging
│   ├── conversation_manager.py  # Chat flow management
│   └── document_service.py      # File handling
├── data/               # Configuration data
│   └── services_config.json    # Service definitions
└── utils/              # Utility functions
    └── helpers.py      # Helper functions
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project
cd Gov-Project

# Install dependencies
pip install -r requirements.txt

# Run setup script
python setup.py
```

### 2. Configuration

Update `.env` file with your Twilio credentials:

```env
# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Other settings...
```

### 3. Run the Application

```bash
# Start the server
python main.py

# Or with uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. Configure Twilio Webhook

Set your Twilio WhatsApp webhook URL to:
```
https://your-domain.com/webhook/whatsapp
```

## 💬 Chatbot Flow

### Step 1: Greeting & Service Selection
```
👋 Hello! Please select the service category you want to apply for:

1. Medical
2. Town Planning  
3. Civil Registration
4. Revenue

Please reply with the number of your choice.
```

### Step 2: Sub-Service Selection
```
Great! You selected Civil Registration.

Choose the specific service:

1. Birth Certificate
2. Duplicate Birth Certificate
3. Minor Corrections

Please reply with the number of your choice.
```

### Step 3: Document Requirements
```
You selected: Birth Certificate

📋 You will need the following documents:

1. Proof of Birth
2. Identity Proof of Parents
3. Hospital Discharge Summary

📩 Please upload the above documents one by one.
```

### Step 4: Document Upload & Confirmation
```
✅ Received: Proof of Birth

📋 Still needed:
1. Identity Proof of Parents
2. Hospital Discharge Summary

Please upload the next document.
```

### Step 5: Final Submission
```
✅ Application Submitted Successfully!

📋 Application ID: APP-20241202-000001

Your application has been submitted and is under review.
You will be notified about the status updates.

Thank you for using our service! 🙏
```

## 🔧 API Endpoints

### WhatsApp Webhook
- `POST /webhook/whatsapp` - Handle incoming WhatsApp messages
- `GET /webhook/whatsapp` - Webhook verification

### Admin Dashboard
- `GET /admin/stats` - Dashboard statistics
- `GET /admin/users` - List users
- `GET /admin/applications` - List applications
- `GET /admin/conversations` - List conversations
- `PUT /admin/applications/{id}/status` - Update application status
- `POST /admin/services/load-from-config` - Load services from config

### Documentation
- `GET /docs` - Swagger UI documentation
- `GET /redoc` - ReDoc documentation

## 📊 Database Schema

### Users
- Phone number-based identification
- Application history tracking

### Services & Categories
- Hierarchical service structure
- Configurable document requirements

### Applications
- Status tracking (pending, submitted, approved, rejected)
- Document associations

### Conversations
- Multi-step conversation state management
- Message history

### Documents
- File metadata and storage paths
- Application associations

## 🛠️ Configuration

### Service Configuration (`data/services_config.json`)

```json
{
  "service_categories": [
    {
      "name": "Civil Registration",
      "description": "Birth, death, and marriage certificates",
      "services": [
        {
          "name": "Birth Certificate",
          "description": "Original birth certificate",
          "required_documents": [
            "Proof of Birth",
            "Identity Proof of Parents",
            "Hospital Discharge Summary"
          ]
        }
      ]
    }
  ]
}
```

### Environment Variables

```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Application Configuration
SECRET_KEY=your-secret-key
DEBUG=True
HOST=0.0.0.0
PORT=8000

# Database
DATABASE_URL=sqlite:///./chatbot.db

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********  # 10MB
ALLOWED_EXTENSIONS=pdf,jpg,jpeg,png,doc,docx
```

## 🔒 Security Features

- Request validation for Twilio webhooks
- File type and size validation
- Secure file storage with unique filenames
- Phone number validation and sanitization

## 📱 Supported File Types

- **Images**: JPG, JPEG, PNG
- **Documents**: PDF, DOC, DOCX

## 🚀 Deployment

### Local Development
```bash
python main.py
```

### Production Deployment
```bash
# Using gunicorn
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Using Docker (create Dockerfile)
docker build -t whatsapp-chatbot .
docker run -p 8000:8000 whatsapp-chatbot
```

## 🧪 Testing

### Manual Testing
1. Use the `/webhook/send-message` endpoint to test message sending
2. Use ngrok for local webhook testing:
   ```bash
   ngrok http 8000
   ```

### Admin Interface
- Access admin endpoints at `/admin/*`
- Monitor applications and conversations
- Update application statuses

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the admin dashboard at `/admin/stats`
- Check application logs for debugging

## 🔄 Updates

To update services configuration:
1. Modify `data/services_config.json`
2. Call `POST /admin/services/load-from-config`
3. Services will be updated in the database

---

**Built with ❤️ using FastAPI, SQLAlchemy, and Twilio WhatsApp API**
