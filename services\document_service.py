import os
import uuid
import aiofiles
import httpx
from typing import Op<PERSON>, <PERSON><PERSON>
from fastapi import UploadFile, HTTPException
from sqlalchemy.orm import Session
from models.service import Document, Application
from services.ocr_service import OCRService
from config import settings
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self, db: Session):
        self.db = db
        self.upload_dir = settings.upload_dir
        self.max_file_size = settings.max_file_size
        self.allowed_extensions = settings.allowed_extensions

        # Initialize OCR service if enabled
        self.ocr_service = OCRService() if settings.ocr_enabled else None
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename"""
        return filename.split('.')[-1].lower() if '.' in filename else ''
    
    def _is_allowed_file(self, filename: str) -> bool:
        """Check if file extension is allowed"""
        extension = self._get_file_extension(filename)
        return extension in self.allowed_extensions

    def _validate_file_content(self, file_content: bytes, filename: str) -> tuple[bool, str]:
        """Validate file content and detect file type"""
        try:
            # Check file size
            if len(file_content) > self.max_file_size:
                return False, f"File too large. Maximum size: {self.max_file_size // (1024*1024)}MB"

            if len(file_content) < 100:  # Minimum file size
                return False, "File too small or corrupted"

            # Check file signatures (magic numbers)
            file_signatures = {
                b'\x25\x50\x44\x46': 'pdf',  # PDF
                b'\xFF\xD8\xFF': 'jpg',       # JPEG
                b'\x89\x50\x4E\x47': 'png',   # PNG
                b'\x50\x4B\x03\x04': 'docx',  # DOCX/ZIP
                b'\xD0\xCF\x11\xE0': 'doc',   # DOC
            }

            detected_type = None
            for signature, file_type in file_signatures.items():
                if file_content.startswith(signature):
                    detected_type = file_type
                    break

            # Get extension from filename
            extension = self._get_file_extension(filename)

            # Validate that file content matches extension
            if detected_type:
                if extension == 'jpeg':
                    extension = 'jpg'  # Normalize

                if detected_type != extension and not (detected_type == 'jpg' and extension in ['jpg', 'jpeg']):
                    return False, f"File content doesn't match extension. Detected: {detected_type}, Expected: {extension}"

            return True, "File validation passed"

        except Exception as e:
            return False, f"File validation error: {str(e)}"
    
    def _generate_unique_filename(self, original_filename: str) -> str:
        """Generate unique filename while preserving extension"""
        extension = self._get_file_extension(original_filename)
        unique_id = str(uuid.uuid4())
        return f"{unique_id}.{extension}" if extension else unique_id
    
    async def download_from_url(self, media_url: str) -> Tuple[bytes, str]:
        """Download file from URL (for WhatsApp media)"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(media_url)
                response.raise_for_status()
                
                # Try to get filename from Content-Disposition header
                content_disposition = response.headers.get('content-disposition', '')
                filename = 'document'
                
                if 'filename=' in content_disposition:
                    filename = content_disposition.split('filename=')[1].strip('"')
                else:
                    # Try to guess from content type
                    content_type = response.headers.get('content-type', '')
                    if 'image/jpeg' in content_type:
                        filename = 'document.jpg'
                    elif 'image/png' in content_type:
                        filename = 'document.png'
                    elif 'application/pdf' in content_type:
                        filename = 'document.pdf'
                
                return response.content, filename
                
        except Exception as e:
            logger.error(f"Failed to download file from URL {media_url}: {str(e)}")
            raise HTTPException(status_code=400, detail="Failed to download file")
    
    async def save_file(self, file_content: bytes, filename: str, application_id: int, document_type: str) -> Document:
        """Save file to disk and create database record"""
        try:
            # Validate file extension
            if not self._is_allowed_file(filename):
                raise HTTPException(status_code=400, detail="File type not allowed")

            # Validate file content
            is_valid, validation_message = self._validate_file_content(file_content, filename)
            if not is_valid:
                raise HTTPException(status_code=400, detail=validation_message)
            
            # Generate unique filename and path
            stored_filename = self._generate_unique_filename(filename)
            
            # Create directory structure: uploads/application_id/
            app_dir = os.path.join(self.upload_dir, str(application_id))
            os.makedirs(app_dir, exist_ok=True)
            
            file_path = os.path.join(app_dir, stored_filename)
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # Create database record
            document = Document(
                application_id=application_id,
                document_type=document_type,
                original_filename=filename,
                stored_filename=stored_filename,
                file_path=file_path,
                file_size=len(file_content),
                mime_type=self._get_mime_type(filename)
            )

            self.db.add(document)
            self.db.commit()
            self.db.refresh(document)

            # Perform OCR if enabled
            ocr_result = None
            if self.ocr_service:
                try:
                    ocr_result = self.ocr_service.process_document(
                        file_path, document_type, application_id
                    )
                    logger.info(f"OCR processing completed for {filename}")
                except Exception as e:
                    logger.error(f"OCR processing failed for {filename}: {str(e)}")

            logger.info(f"File saved successfully: {file_path}")

            # Add OCR result to document object for return
            if hasattr(document, '__dict__'):
                document.ocr_result = ocr_result

            return document
            
        except Exception as e:
            logger.error(f"Failed to save file: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to save file")
    
    async def save_from_upload(self, file: UploadFile, application_id: int, document_type: str) -> Document:
        """Save uploaded file"""
        content = await file.read()
        return await self.save_file(content, file.filename, application_id, document_type)
    
    async def save_from_url(self, media_url: str, application_id: int, document_type: str) -> Document:
        """Save file from URL (WhatsApp media)"""
        content, filename = await self.download_from_url(media_url)
        return await self.save_file(content, filename, application_id, document_type)
    
    def _get_mime_type(self, filename: str) -> str:
        """Get MIME type based on file extension"""
        extension = self._get_file_extension(filename)
        mime_types = {
            'pdf': 'application/pdf',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
        return mime_types.get(extension, 'application/octet-stream')
    
    def get_application_documents(self, application_id: int) -> list:
        """Get all documents for an application"""
        return self.db.query(Document).filter(Document.application_id == application_id).all()
    
    def delete_document(self, document_id: int) -> bool:
        """Delete document file and database record"""
        try:
            document = self.db.query(Document).filter(Document.id == document_id).first()
            if not document:
                return False
            
            # Delete file from disk
            if os.path.exists(document.file_path):
                os.remove(document.file_path)
            
            # Delete database record
            self.db.delete(document)
            self.db.commit()
            
            logger.info(f"Document deleted successfully: {document.file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete document: {str(e)}")
            return False
