import mysql.connector
import pymysql
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from config import settings

logger = logging.getLogger(__name__)

class ExternalDBService:
    """Service for storing OCR results in external MySQL database"""
    
    def __init__(self):
        self.host = settings.external_db_host
        self.database = settings.external_db_name
        self.user = settings.external_db_user
        self.password = settings.external_db_password
        self.port = settings.external_db_port
        
        logger.info(f"External DB Service initialized for {self.host}:{self.port}/{self.database}")
        
        # Create tables if they don't exist
        self._create_tables()
    
    def get_connection(self):
        """Get database connection"""
        try:
            connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            return connection
        except Exception as e:
            logger.error(f"Failed to connect to external database: {str(e)}")
            raise
    
    def _create_tables(self):
        """Create necessary tables for OCR storage"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # Create OCR results table
            create_ocr_table = """
            CREATE TABLE IF NOT EXISTS ocr_results (
                id INT AUTO_INCREMENT PRIMARY KEY,
                application_id INT NOT NULL,
                document_type VARCHAR(100) NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path TEXT,
                file_type VARCHAR(20),
                file_size BIGINT,
                ocr_success BOOLEAN DEFAULT FALSE,
                full_text LONGTEXT,
                simple_text LONGTEXT,
                text_blocks JSON,
                total_blocks INT DEFAULT 0,
                average_confidence DECIMAL(5,2) DEFAULT 0.00,
                ocr_language VARCHAR(50) DEFAULT 'eng+hin',
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_application_id (application_id),
                INDEX idx_document_type (document_type),
                INDEX idx_processed_at (processed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            cursor.execute(create_ocr_table)
            
            # Create extracted information table
            create_extracted_info_table = """
            CREATE TABLE IF NOT EXISTS extracted_information (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ocr_result_id INT NOT NULL,
                application_id INT NOT NULL,
                document_type VARCHAR(100) NOT NULL,
                extracted_data JSON,
                confidence_score DECIMAL(5,2) DEFAULT 0.00,
                extraction_method VARCHAR(50) DEFAULT 'regex',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (ocr_result_id) REFERENCES ocr_results(id) ON DELETE CASCADE,
                INDEX idx_application_id (application_id),
                INDEX idx_document_type (document_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            cursor.execute(create_extracted_info_table)
            
            # Create OCR statistics table
            create_stats_table = """
            CREATE TABLE IF NOT EXISTS ocr_statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE NOT NULL,
                total_documents INT DEFAULT 0,
                successful_ocr INT DEFAULT 0,
                failed_ocr INT DEFAULT 0,
                average_confidence DECIMAL(5,2) DEFAULT 0.00,
                total_text_length BIGINT DEFAULT 0,
                processing_time_avg DECIMAL(8,2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_date (date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            cursor.execute(create_stats_table)
            
            connection.close()
            logger.info("External database tables created/verified successfully")
            
        except Exception as e:
            logger.error(f"Failed to create tables in external database: {str(e)}")
            raise
    
    def store_ocr_result(self, ocr_result: Dict, application_id: int, document_type: str) -> Optional[int]:
        """Store OCR result in external database"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # Prepare data for insertion
            insert_query = """
            INSERT INTO ocr_results (
                application_id, document_type, file_name, file_path, file_type, file_size,
                ocr_success, full_text, simple_text, text_blocks, total_blocks,
                average_confidence, ocr_language, processed_at
            ) VALUES (
                %(application_id)s, %(document_type)s, %(file_name)s, %(file_path)s, 
                %(file_type)s, %(file_size)s, %(ocr_success)s, %(full_text)s, 
                %(simple_text)s, %(text_blocks)s, %(total_blocks)s, 
                %(average_confidence)s, %(ocr_language)s, %(processed_at)s
            )
            """
            
            # Prepare data
            data = {
                'application_id': application_id,
                'document_type': document_type,
                'file_name': ocr_result.get('file_name', ''),
                'file_path': ocr_result.get('file_path', ''),
                'file_type': ocr_result.get('file_type', ''),
                'file_size': len(ocr_result.get('full_text', '')),
                'ocr_success': ocr_result.get('success', False),
                'full_text': ocr_result.get('full_text', ''),
                'simple_text': ocr_result.get('simple_text', ''),
                'text_blocks': json.dumps(ocr_result.get('text_blocks', [])),
                'total_blocks': ocr_result.get('total_blocks', 0),
                'average_confidence': ocr_result.get('average_confidence', 0.0),
                'ocr_language': ocr_result.get('ocr_language', 'eng+hin'),
                'processed_at': datetime.now()
            }
            
            cursor.execute(insert_query, data)
            ocr_result_id = cursor.lastrowid
            
            connection.close()
            
            logger.info(f"OCR result stored in external DB with ID: {ocr_result_id}")
            
            # Update daily statistics
            self._update_daily_stats(ocr_result.get('success', False), 
                                   ocr_result.get('average_confidence', 0.0),
                                   len(ocr_result.get('full_text', '')))
            
            return ocr_result_id
            
        except Exception as e:
            logger.error(f"Failed to store OCR result in external database: {str(e)}")
            return None
    
    def store_extracted_information(self, ocr_result_id: int, application_id: int, 
                                  document_type: str, extracted_data: Dict) -> Optional[int]:
        """Store extracted information from OCR result"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            insert_query = """
            INSERT INTO extracted_information (
                ocr_result_id, application_id, document_type, extracted_data, confidence_score
            ) VALUES (
                %(ocr_result_id)s, %(application_id)s, %(document_type)s, 
                %(extracted_data)s, %(confidence_score)s
            )
            """
            
            data = {
                'ocr_result_id': ocr_result_id,
                'application_id': application_id,
                'document_type': document_type,
                'extracted_data': json.dumps(extracted_data),
                'confidence_score': extracted_data.get('confidence', 0.0)
            }
            
            cursor.execute(insert_query, data)
            extracted_info_id = cursor.lastrowid
            
            connection.close()
            
            logger.info(f"Extracted information stored with ID: {extracted_info_id}")
            return extracted_info_id
            
        except Exception as e:
            logger.error(f"Failed to store extracted information: {str(e)}")
            return None
    
    def get_ocr_results_by_application(self, application_id: int) -> List[Dict]:
        """Get all OCR results for an application"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            query = """
            SELECT * FROM ocr_results 
            WHERE application_id = %s 
            ORDER BY created_at DESC
            """
            
            cursor.execute(query, (application_id,))
            results = cursor.fetchall()
            
            connection.close()
            
            # Parse JSON fields
            for result in results:
                if result['text_blocks']:
                    result['text_blocks'] = json.loads(result['text_blocks'])
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get OCR results for application {application_id}: {str(e)}")
            return []
    
    def get_extracted_information(self, application_id: int) -> List[Dict]:
        """Get extracted information for an application"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            query = """
            SELECT ei.*, ocr.file_name, ocr.document_type
            FROM extracted_information ei
            JOIN ocr_results ocr ON ei.ocr_result_id = ocr.id
            WHERE ei.application_id = %s
            ORDER BY ei.created_at DESC
            """
            
            cursor.execute(query, (application_id,))
            results = cursor.fetchall()
            
            connection.close()
            
            # Parse JSON fields
            for result in results:
                if result['extracted_data']:
                    result['extracted_data'] = json.loads(result['extracted_data'])
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get extracted information for application {application_id}: {str(e)}")
            return []
    
    def _update_daily_stats(self, success: bool, confidence: float, text_length: int):
        """Update daily OCR statistics"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            today = datetime.now().date()
            
            # Check if record exists for today
            check_query = "SELECT id FROM ocr_statistics WHERE date = %s"
            cursor.execute(check_query, (today,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record
                update_query = """
                UPDATE ocr_statistics SET
                    total_documents = total_documents + 1,
                    successful_ocr = successful_ocr + %s,
                    failed_ocr = failed_ocr + %s,
                    total_text_length = total_text_length + %s,
                    average_confidence = (
                        (average_confidence * (total_documents - 1) + %s) / total_documents
                    )
                WHERE date = %s
                """
                cursor.execute(update_query, (
                    1 if success else 0,
                    0 if success else 1,
                    text_length,
                    confidence,
                    today
                ))
            else:
                # Insert new record
                insert_query = """
                INSERT INTO ocr_statistics (
                    date, total_documents, successful_ocr, failed_ocr,
                    average_confidence, total_text_length
                ) VALUES (%s, 1, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    today,
                    1 if success else 0,
                    0 if success else 1,
                    confidence,
                    text_length
                ))
            
            connection.close()
            
        except Exception as e:
            logger.error(f"Failed to update daily statistics: {str(e)}")
    
    def get_ocr_statistics(self, days: int = 30) -> List[Dict]:
        """Get OCR statistics for the last N days"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            query = """
            SELECT * FROM ocr_statistics 
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
            ORDER BY date DESC
            """
            
            cursor.execute(query, (days,))
            results = cursor.fetchall()
            
            connection.close()
            return results
            
        except Exception as e:
            logger.error(f"Failed to get OCR statistics: {str(e)}")
            return []
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            connection.close()
            
            logger.info("External database connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"External database connection test failed: {str(e)}")
            return False
