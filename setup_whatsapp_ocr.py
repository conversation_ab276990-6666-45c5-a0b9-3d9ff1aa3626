#!/usr/bin/env python3
"""
Setup script for WhatsApp OCR Chatbot
Installs dependencies and configures the system
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_python_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed"""
    print("🔍 Checking Tesseract OCR installation...")
    
    # Common Tesseract paths on Windows
    tesseract_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
        "tesseract"  # If in PATH
    ]
    
    for path in tesseract_paths:
        if os.path.exists(path) or shutil.which(path):
            print(f"✅ Tesseract found at: {path}")
            return path
    
    print("❌ Tesseract OCR not found")
    print("📥 Please install Tesseract OCR:")
    print("   1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
    print("   2. Install to: C:\\Program Files\\Tesseract-OCR\\")
    print("   3. Add to PATH or update TESSERACT_PATH in .env")
    return None

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        r"C:\Users\<USER>\Desktop\New folder",
        "logs",
        "uploads"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"⚠️ Could not create directory {directory}: {e}")

def setup_environment():
    """Setup environment configuration"""
    print("⚙️ Setting up environment...")
    
    # Copy .env.example to .env if it doesn't exist
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ Created .env file from template")
        else:
            # Create basic .env file
            env_content = f"""# WhatsApp API Configuration
WHATSAPP_API_KEY=67fd47ab51b34699a1822c669b5d3f99
WHATSAPP_PHONE_NUMBER=**********
WHATSAPP_API_URL=https://api.whatsapp.com/send

# File Upload Configuration
UPLOAD_DIR=C:\\Users\\<USER>\\Desktop\\New folder
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=pdf,jpg,jpeg,png,doc,docx

# OCR Configuration
OCR_ENABLED=true
OCR_OUTPUT_DIR=C:\\Users\\<USER>\\Desktop\\New folder
OCR_LANGUAGE=eng+hin
TESSERACT_PATH=C:\\Program Files\\Tesseract-OCR\\tesseract.exe

# Database Configuration
DATABASE_URL=sqlite:///./chatbot.db

# FastAPI Configuration
SECRET_KEY=whatsapp-chatbot-secret-key
DEBUG=true
HOST=0.0.0.0
PORT=8000
"""
            with open('.env', 'w') as f:
                f.write(env_content)
            print("✅ Created .env file with default configuration")
    else:
        print("⏭️ .env file already exists")

def setup_database():
    """Setup database and load initial data"""
    print("🗄️ Setting up database...")
    try:
        subprocess.run([sys.executable, "setup.py"], check=True)
        print("✅ Database setup completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Database setup failed: {e}")
        return False

def test_ocr():
    """Test OCR functionality"""
    print("🔍 Testing OCR functionality...")
    try:
        import pytesseract
        from PIL import Image
        import numpy as np
        
        # Create a simple test image with text
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a white image with black text
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        draw.text((10, 30), "Test OCR Text", fill='black', font=font)
        
        # Save test image
        test_image_path = "test_ocr.png"
        img.save(test_image_path)
        
        # Test OCR
        text = pytesseract.image_to_string(img)
        
        # Clean up
        os.remove(test_image_path)
        
        if "test" in text.lower() or "ocr" in text.lower():
            print("✅ OCR test successful")
            return True
        else:
            print(f"⚠️ OCR test returned: '{text.strip()}'")
            return True  # Still consider it working
            
    except Exception as e:
        print(f"❌ OCR test failed: {e}")
        return False

def create_test_script():
    """Create a test script for WhatsApp integration"""
    print("📝 Creating test script...")
    
    test_script = '''#!/usr/bin/env python3
"""
Test script for WhatsApp OCR Chatbot
"""

import requests
import json

def test_whatsapp_chatbot():
    """Test the WhatsApp chatbot functionality"""
    base_url = "http://localhost:8000"
    
    print("🤖 Testing WhatsApp OCR Chatbot")
    print("=" * 40)
    
    # Test 1: Health check
    print("1. Testing health check...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print("❌ Health check failed")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test 2: WhatsApp status
    print("\\n2. Testing WhatsApp status...")
    try:
        response = requests.get(f"{base_url}/webhook/whatsapp-status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ WhatsApp API configured: {status.get('api_key_configured')}")
            print(f"📱 Phone number: {status.get('phone_number')}")
            print(f"🔍 OCR enabled: {status.get('ocr_enabled')}")
        else:
            print("❌ WhatsApp status check failed")
    except Exception as e:
        print(f"❌ WhatsApp status error: {e}")
    
    # Test 3: Conversation flow
    print("\\n3. Testing conversation flow...")
    try:
        response = requests.post(f"{base_url}/webhook/test-conversation", json={
            "phone_number": "**********",
            "message": "start"
        })
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Conversation test: {result.get('status')}")
            print(f"🤖 Bot response: {result.get('bot_response', '')[:100]}...")
        else:
            print("❌ Conversation test failed")
    except Exception as e:
        print(f"❌ Conversation test error: {e}")
    
    print("\\n🎉 Test completed!")

if __name__ == "__main__":
    test_whatsapp_chatbot()
'''
    
    with open('test_whatsapp.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Test script created: test_whatsapp.py")

def main():
    """Main setup function"""
    print("🚀 Setting up WhatsApp OCR Chatbot")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Create directories
    create_directories()
    
    # Setup environment
    setup_environment()
    
    # Install Python dependencies
    if not install_python_dependencies():
        return False
    
    # Check Tesseract
    tesseract_path = check_tesseract()
    if not tesseract_path:
        print("⚠️ Tesseract not found. OCR functionality will be disabled.")
    
    # Setup database
    if not setup_database():
        return False
    
    # Test OCR if Tesseract is available
    if tesseract_path:
        test_ocr()
    
    # Create test script
    create_test_script()
    
    print("\\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\\n📋 Next steps:")
    print("1. Verify your WhatsApp API key in .env file")
    print("2. Start the server: python main.py")
    print("3. Test the chatbot: python test_whatsapp.py")
    print("4. Configure your WhatsApp webhook URL")
    print("\\n🌐 Server will run at: http://localhost:8000")
    print("📖 API docs: http://localhost:8000/docs")
    print("📱 WhatsApp webhook: http://localhost:8000/webhook/whatsapp-api")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
