from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, DateTime, Text, <PERSON>olean, Foreign<PERSON>ey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.database import Base

class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    current_step = Column(String(50), default="greeting")  # greeting, category_selection, service_selection, document_upload, confirmation
    current_service_id = Column(Integer, ForeignKey("services.id"), nullable=True)
    current_application_id = Column(Integer, ForeignKey("applications.id"), nullable=True)
    context_data = Column(JSON, default={})  # Store conversation context
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation")

class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    message_type = Column(String(20), nullable=False)  # incoming, outgoing
    content = Column(Text, nullable=False)
    media_url = Column(String(500), nullable=True)  # For file attachments
    twilio_message_sid = Column(String(100), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
