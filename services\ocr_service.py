import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import pytesseract
import cv2
import numpy as np
from PIL import Image
from pdf2image import convert_from_path
from config import settings

logger = logging.getLogger(__name__)

class OCRService:
    def __init__(self):
        self.tesseract_path = settings.tesseract_path
        self.ocr_language = settings.ocr_language
        self.output_dir = Path(settings.ocr_output_dir)

        # Set tesseract path if specified
        if self.tesseract_path and os.path.exists(self.tesseract_path):
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_path

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Initialize external database service
        try:
            from services.external_db_service import ExternalDBService
            self.external_db = ExternalDBService()
            self.use_external_db = True
            logger.info("External database service initialized for OCR storage")
        except Exception as e:
            logger.warning(f"External database not available, using local storage: {str(e)}")
            self.external_db = None
            self.use_external_db = False

        logger.info(f"OCR Service initialized with language: {self.ocr_language}")
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for better OCR results"""
        try:
            # Convert to grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations to clean up
            kernel = np.ones((1, 1), np.uint8)
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return processed
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            return image
    
    def extract_text_from_image(self, image_path: str) -> Dict:
        """Extract text from image using OCR"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                # Try with PIL for different formats
                pil_image = Image.open(image_path)
                image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Perform OCR with detailed data
            ocr_data = pytesseract.image_to_data(
                processed_image, 
                lang=self.ocr_language, 
                output_type=pytesseract.Output.DICT
            )
            
            # Extract text with confidence
            text_blocks = []
            full_text = ""
            
            for i in range(len(ocr_data['text'])):
                text = ocr_data['text'][i].strip()
                confidence = int(ocr_data['conf'][i])
                
                if text and confidence > 30:  # Filter low confidence text
                    text_blocks.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': {
                            'x': ocr_data['left'][i],
                            'y': ocr_data['top'][i],
                            'width': ocr_data['width'][i],
                            'height': ocr_data['height'][i]
                        }
                    })
                    full_text += text + " "
            
            # Get simple text extraction as well
            simple_text = pytesseract.image_to_string(processed_image, lang=self.ocr_language)
            
            return {
                'success': True,
                'full_text': full_text.strip(),
                'simple_text': simple_text.strip(),
                'text_blocks': text_blocks,
                'total_blocks': len(text_blocks),
                'average_confidence': sum(block['confidence'] for block in text_blocks) / len(text_blocks) if text_blocks else 0
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from image {image_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'full_text': '',
                'simple_text': '',
                'text_blocks': [],
                'total_blocks': 0,
                'average_confidence': 0
            }
    
    def extract_text_from_pdf(self, pdf_path: str) -> Dict:
        """Extract text from PDF by converting to images first"""
        try:
            # Convert PDF to images
            images = convert_from_path(pdf_path, dpi=300)
            
            all_text_blocks = []
            full_text = ""
            simple_text = ""
            
            for page_num, image in enumerate(images):
                # Save temporary image
                temp_image_path = self.output_dir / f"temp_page_{page_num}.png"
                image.save(temp_image_path)
                
                # Extract text from this page
                page_result = self.extract_text_from_image(str(temp_image_path))
                
                if page_result['success']:
                    # Add page information to text blocks
                    for block in page_result['text_blocks']:
                        block['page'] = page_num + 1
                    
                    all_text_blocks.extend(page_result['text_blocks'])
                    full_text += f"\\n--- Page {page_num + 1} ---\\n" + page_result['full_text'] + "\\n"
                    simple_text += f"\\n--- Page {page_num + 1} ---\\n" + page_result['simple_text'] + "\\n"
                
                # Clean up temporary file
                if temp_image_path.exists():
                    temp_image_path.unlink()
            
            return {
                'success': True,
                'full_text': full_text.strip(),
                'simple_text': simple_text.strip(),
                'text_blocks': all_text_blocks,
                'total_blocks': len(all_text_blocks),
                'total_pages': len(images),
                'average_confidence': sum(block['confidence'] for block in all_text_blocks) / len(all_text_blocks) if all_text_blocks else 0
            }
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF {pdf_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'full_text': '',
                'simple_text': '',
                'text_blocks': [],
                'total_blocks': 0,
                'total_pages': 0,
                'average_confidence': 0
            }
    
    def process_document(self, file_path: str, document_type: str, application_id: int) -> Dict:
        """Process document and extract text using OCR"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return {'success': False, 'error': 'File not found'}
            
            # Determine file type and process accordingly
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.pdf':
                ocr_result = self.extract_text_from_pdf(str(file_path))
            elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                ocr_result = self.extract_text_from_image(str(file_path))
            else:
                return {'success': False, 'error': f'Unsupported file type: {file_extension}'}
            
            # Add metadata
            ocr_result.update({
                'file_path': str(file_path),
                'file_name': file_path.name,
                'file_type': file_extension,
                'document_type': document_type,
                'application_id': application_id,
                'processed_at': datetime.now().isoformat(),
                'ocr_language': self.ocr_language
            })
            
            # Save OCR result to JSON file (local backup)
            json_filename = f"ocr_{application_id}_{document_type}_{file_path.stem}.json"
            json_path = self.output_dir / json_filename

            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(ocr_result, f, indent=2, ensure_ascii=False)

            ocr_result['json_path'] = str(json_path)

            # Store in external database if available
            if self.use_external_db and self.external_db:
                try:
                    ocr_result_id = self.external_db.store_ocr_result(
                        ocr_result, application_id, document_type
                    )
                    if ocr_result_id:
                        ocr_result['db_id'] = ocr_result_id
                        logger.info(f"OCR result stored in external database with ID: {ocr_result_id}")

                        # Extract and store key information
                        extracted_info = self.extract_key_information(ocr_result, document_type)
                        if extracted_info:
                            self.external_db.store_extracted_information(
                                ocr_result_id, application_id, document_type, extracted_info
                            )
                            logger.info(f"Extracted information stored for OCR ID: {ocr_result_id}")

                except Exception as e:
                    logger.error(f"Failed to store OCR result in external database: {str(e)}")

            logger.info(f"OCR completed for {file_path.name}. Results saved locally and to database")

            return ocr_result
            
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'file_path': str(file_path) if 'file_path' in locals() else '',
                'processed_at': datetime.now().isoformat()
            }
    
    def extract_key_information(self, ocr_result: Dict, document_type: str) -> Dict:
        """Extract key information based on document type"""
        try:
            text = ocr_result.get('full_text', '').lower()
            extracted_info = {'document_type': document_type}
            
            if document_type.lower() in ['birth_certificate', 'birth certificate']:
                # Extract birth certificate specific information
                import re
                
                # Look for name patterns
                name_patterns = [
                    r'name[:\s]+([a-zA-Z\s]+)',
                    r'child[:\s]+([a-zA-Z\s]+)',
                    r'नाम[:\s]+([a-zA-Z\s]+)'
                ]
                
                for pattern in name_patterns:
                    match = re.search(pattern, text)
                    if match:
                        extracted_info['name'] = match.group(1).strip()
                        break
                
                # Look for date patterns
                date_patterns = [
                    r'date of birth[:\s]+(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                    r'dob[:\s]+(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
                    r'जन्म तिथि[:\s]+(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})'
                ]
                
                for pattern in date_patterns:
                    match = re.search(pattern, text)
                    if match:
                        extracted_info['date_of_birth'] = match.group(1).strip()
                        break
            
            elif document_type.lower() in ['identity_proof', 'id_proof']:
                # Extract ID specific information
                import re
                
                # Look for Aadhaar number
                aadhaar_match = re.search(r'\b\d{4}\s?\d{4}\s?\d{4}\b', text)
                if aadhaar_match:
                    extracted_info['aadhaar_number'] = aadhaar_match.group(0)
                
                # Look for PAN number
                pan_match = re.search(r'\b[A-Z]{5}\d{4}[A-Z]\b', text)
                if pan_match:
                    extracted_info['pan_number'] = pan_match.group(0)
            
            return extracted_info
            
        except Exception as e:
            logger.error(f"Error extracting key information: {str(e)}")
            return {'document_type': document_type, 'error': str(e)}
    
    def get_ocr_summary(self, application_id: int) -> Dict:
        """Get summary of all OCR results for an application"""
        try:
            ocr_files = list(self.output_dir.glob(f"ocr_{application_id}_*.json"))
            
            summary = {
                'application_id': application_id,
                'total_documents': len(ocr_files),
                'documents': []
            }
            
            for ocr_file in ocr_files:
                try:
                    with open(ocr_file, 'r', encoding='utf-8') as f:
                        ocr_data = json.load(f)
                    
                    summary['documents'].append({
                        'file_name': ocr_data.get('file_name', ''),
                        'document_type': ocr_data.get('document_type', ''),
                        'success': ocr_data.get('success', False),
                        'confidence': ocr_data.get('average_confidence', 0),
                        'text_length': len(ocr_data.get('full_text', '')),
                        'json_path': str(ocr_file)
                    })
                except Exception as e:
                    logger.error(f"Error reading OCR file {ocr_file}: {str(e)}")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting OCR summary for application {application_id}: {str(e)}")
            return {'application_id': application_id, 'error': str(e)}
