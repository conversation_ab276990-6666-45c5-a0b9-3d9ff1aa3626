from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
from config import settings
import logging

logger = logging.getLogger(__name__)

class WhatsAppService:
    def __init__(self):
        self.client = Client(settings.twilio_account_sid, settings.twilio_auth_token)
        self.from_number = settings.twilio_whatsapp_number
    
    def send_message(self, to_number: str, message: str) -> bool:
        """Send a text message via WhatsApp"""
        try:
            # Ensure the number is in WhatsApp format
            if not to_number.startswith('whatsapp:'):
                to_number = f'whatsapp:{to_number}'
            
            message = self.client.messages.create(
                body=message,
                from_=self.from_number,
                to=to_number
            )
            
            logger.info(f"Message sent successfully. SID: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message: {str(e)}")
            return False
    
    def send_message_with_media(self, to_number: str, message: str, media_url: str) -> bool:
        """Send a message with media attachment via WhatsApp"""
        try:
            # Ensure the number is in WhatsApp format
            if not to_number.startswith('whatsapp:'):
                to_number = f'whatsapp:{to_number}'
            
            message = self.client.messages.create(
                body=message,
                from_=self.from_number,
                to=to_number,
                media_url=[media_url]
            )
            
            logger.info(f"Message with media sent successfully. SID: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message with media: {str(e)}")
            return False
    
    def create_response(self, message: str) -> MessagingResponse:
        """Create a TwiML response for immediate reply"""
        response = MessagingResponse()
        response.message(message)
        return response
    
    def format_service_list(self, categories: list) -> str:
        """Format service categories as a numbered list"""
        message = "👋 Hello! Please select the service category you want to apply for:\n\n"
        
        for i, category in enumerate(categories, 1):
            message += f"{i}. {category['name']}\n"
        
        message += "\nPlease reply with the number of your choice."
        return message
    
    def format_sub_service_list(self, services: list, category_name: str) -> str:
        """Format sub-services as a numbered list"""
        message = f"Great! You selected *{category_name}*.\n\n"
        message += "Choose the specific service:\n\n"
        
        for i, service in enumerate(services, 1):
            message += f"{i}. {service['name']}\n"
        
        message += "\nPlease reply with the number of your choice."
        return message
    
    def format_document_requirements(self, service_name: str, required_documents: list) -> str:
        """Format required documents list"""
        message = f"You selected: *{service_name}*\n\n"
        message += "📋 You will need the following documents:\n\n"
        
        for i, doc in enumerate(required_documents, 1):
            message += f"{i}. {doc}\n"
        
        message += "\n📩 Please upload the above documents one by one."
        message += "\n\nSend each document as an image or PDF file."
        return message
    
    def format_document_received(self, document_type: str, remaining_docs: list) -> str:
        """Format confirmation message for received document"""
        message = f"✅ Received: *{document_type}*\n\n"
        
        if remaining_docs:
            message += "📋 Still needed:\n"
            for i, doc in enumerate(remaining_docs, 1):
                message += f"{i}. {doc}\n"
            message += "\nPlease upload the next document."
        else:
            message += "🎉 All documents received!\n\n"
            message += "Do you want to submit your application now?\n\n"
            message += "Reply with:\n"
            message += "• *Yes* - Submit application\n"
            message += "• *No* - Cancel and start over"
        
        return message
    
    def format_submission_confirmation(self, application_id: int) -> str:
        """Format final submission confirmation"""
        message = "✅ *Application Submitted Successfully!*\n\n"
        message += f"📋 Application ID: *{application_id}*\n\n"
        message += "Your application has been submitted and is under review.\n"
        message += "You will be notified about the status updates.\n\n"
        message += "Thank you for using our service! 🙏\n\n"
        message += "Type *start* to begin a new application."
        return message
