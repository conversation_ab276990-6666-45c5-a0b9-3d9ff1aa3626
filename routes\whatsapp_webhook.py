from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import Response
from sqlalchemy.orm import Session
from models.database import get_db
from services.conversation_manager import ConversationManager
from services.whatsapp_api_service import WhatsAppAPIService
from config import settings
import logging
import json
from typing import Optional

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/whatsapp-api")
async def whatsapp_api_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """Handle incoming WhatsApp messages from your API provider"""
    try:
        # Get request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))
        
        logger.info(f"Received WhatsApp API webhook: {data}")
        
        # Extract message data (format may vary based on your API provider)
        # Common formats:
        # Format 1: UltraMsg style
        if 'data' in data:
            message_data = data['data']
            from_number = message_data.get('from', '')
            message_body = message_data.get('body', '')
            media_url = message_data.get('media', '')
            message_id = message_data.get('id', '')
        
        # Format 2: Direct format
        else:
            from_number = data.get('from', data.get('phone', ''))
            message_body = data.get('body', data.get('message', data.get('text', '')))
            media_url = data.get('media', data.get('media_url', ''))
            message_id = data.get('id', data.get('message_id', ''))
        
        # Clean phone number
        clean_number = from_number.replace('whatsapp:', '').replace('+', '').replace('@c.us', '')
        
        logger.info(f"Processing message from {clean_number}: {message_body}")
        
        # Initialize services
        conversation_manager = ConversationManager(db)
        whatsapp_api = WhatsAppAPIService()
        
        # Process the message
        response_message = conversation_manager.process_message(
            phone_number=clean_number,
            message_content=message_body,
            media_url=media_url if media_url else None,
            twilio_sid=message_id
        )
        
        # Send response back via WhatsApp API
        if response_message:
            success = whatsapp_api.send_message(clean_number, response_message)
            if success:
                logger.info(f"Response sent successfully to {clean_number}")
            else:
                logger.error(f"Failed to send response to {clean_number}")
        
        return {"status": "success", "message": "Message processed"}
        
    except Exception as e:
        logger.error(f"Error processing WhatsApp API webhook: {str(e)}")
        return {"status": "error", "message": str(e)}

@router.get("/whatsapp-api")
async def whatsapp_api_webhook_verification():
    """Handle WhatsApp API webhook verification"""
    return {"status": "WhatsApp API webhook is active", "number": settings.whatsapp_phone_number}

@router.post("/send-whatsapp")
async def send_whatsapp_message(
    request: dict,
    db: Session = Depends(get_db)
):
    """Manual endpoint to send WhatsApp messages"""
    try:
        phone_number = request.get("phone_number")
        message = request.get("message")
        
        if not phone_number or not message:
            raise HTTPException(status_code=400, detail="phone_number and message are required")
        
        whatsapp_api = WhatsAppAPIService()
        success = whatsapp_api.send_message(phone_number, message)
        
        if success:
            return {"status": "success", "message": "Message sent successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to send message")
            
    except Exception as e:
        logger.error(f"Error sending WhatsApp message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-conversation")
async def test_conversation_flow(
    request: dict,
    db: Session = Depends(get_db)
):
    """Test endpoint for conversation flow"""
    try:
        phone_number = request.get("phone_number", "8983987726")
        message = request.get("message", "start")
        
        conversation_manager = ConversationManager(db)
        whatsapp_api = WhatsAppAPIService()
        
        # Process message
        response_message = conversation_manager.process_message(
            phone_number=phone_number,
            message_content=message,
            media_url=None,
            twilio_sid=f"test_{phone_number}"
        )
        
        # Send via WhatsApp API
        if response_message:
            success = whatsapp_api.send_message(phone_number, response_message)
            
            return {
                "status": "success",
                "phone_number": phone_number,
                "user_message": message,
                "bot_response": response_message,
                "sent_via_whatsapp": success
            }
        else:
            return {
                "status": "error",
                "message": "No response generated"
            }
            
    except Exception as e:
        logger.error(f"Error in test conversation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/whatsapp-status")
async def get_whatsapp_status():
    """Get WhatsApp API status"""
    return {
        "api_key_configured": bool(settings.whatsapp_api_key),
        "phone_number": settings.whatsapp_phone_number,
        "api_url": settings.whatsapp_api_url,
        "ocr_enabled": settings.ocr_enabled,
        "upload_dir": settings.upload_dir
    }
