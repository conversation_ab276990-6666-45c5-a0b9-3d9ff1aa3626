# WhatsApp API Configuration
WHATSAPP_API_KEY=67fd47ab51b34699a1822c669b5d3f99
WHATSAPP_PHONE_NUMBER=**********
WHATSAPP_API_URL=https://api.whatsapp.com/send

# Twilio WhatsApp Configuration (Backup)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# FastAPI Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
HOST=0.0.0.0
PORT=8000

# Database Configuration
DATABASE_URL=sqlite:///./chatbot.db

# File Upload Configuration
UPLOAD_DIR=C:\Users\<USER>\Desktop\New folder
MAX_FILE_SIZE=********  # 10MB in bytes
ALLOWED_EXTENSIONS=pdf,jpg,jpeg,png,doc,docx

# OCR Configuration
OCR_ENABLED=true
OCR_OUTPUT_DIR=C:\Users\<USER>\Desktop\New folder
OCR_LANGUAGE=eng+hin  # English + Hindi
TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
