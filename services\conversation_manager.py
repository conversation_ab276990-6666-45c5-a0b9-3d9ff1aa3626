from sqlalchemy.orm import Session
from models.user import User
from models.conversation import Conversation, Message
from models.service import ServiceCategory, Service, Application
from services.whatsapp_service import WhatsAppService
from services.whatsapp_api_service import WhatsAppAPIService
from services.document_service import DocumentService
import json
import logging

logger = logging.getLogger(__name__)

class ConversationManager:
    def __init__(self, db: Session):
        self.db = db
        self.whatsapp = WhatsAppService()
        self.whatsapp_api = WhatsAppAPIService()
        self.document_service = DocumentService(db)
    
    def get_or_create_user(self, phone_number: str) -> User:
        """Get existing user or create new one"""
        # Clean phone number format
        clean_number = phone_number.replace('whatsapp:', '').replace('+', '')
        
        user = self.db.query(User).filter(User.phone_number == clean_number).first()
        if not user:
            user = User(phone_number=clean_number)
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
        
        return user
    
    def get_or_create_conversation(self, user: User) -> Conversation:
        """Get active conversation or create new one"""
        conversation = self.db.query(Conversation).filter(
            Conversation.user_id == user.id,
            Conversation.is_active == True
        ).first()
        
        if not conversation:
            conversation = Conversation(
                user_id=user.id,
                current_step="greeting",
                context_data={}
            )
            self.db.add(conversation)
            self.db.commit()
            self.db.refresh(conversation)
        
        return conversation
    
    def save_message(self, conversation: Conversation, message_type: str, content: str, media_url: str = None, twilio_sid: str = None):
        """Save message to database"""
        message = Message(
            conversation_id=conversation.id,
            message_type=message_type,
            content=content,
            media_url=media_url,
            twilio_message_sid=twilio_sid
        )
        self.db.add(message)
        self.db.commit()
    
    def load_services_config(self) -> dict:
        """Load services configuration from JSON file"""
        try:
            with open('data/services_config.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load services config: {str(e)}")
            return {"service_categories": []}
    
    def handle_greeting(self, conversation: Conversation) -> str:
        """Handle initial greeting and show service categories"""
        config = self.load_services_config()
        categories = config.get("service_categories", [])
        
        # Update conversation context
        conversation.context_data = {"categories": categories}
        conversation.current_step = "category_selection"
        self.db.commit()
        
        return self.whatsapp.format_service_list(categories)
    
    def handle_category_selection(self, conversation: Conversation, user_input: str) -> str:
        """Handle service category selection"""
        try:
            choice = int(user_input.strip()) - 1
            categories = conversation.context_data.get("categories", [])

            if 0 <= choice < len(categories):
                selected_category = categories[choice]

                # Get services from database for this category
                from models.service import ServiceCategory, Service
                db_category = self.db.query(ServiceCategory).filter(
                    ServiceCategory.name == selected_category["name"]
                ).first()

                if db_category:
                    db_services = self.db.query(Service).filter(
                        Service.category_id == db_category.id,
                        Service.is_active == True
                    ).all()

                    # Convert to format expected by conversation
                    services = []
                    for service in db_services:
                        services.append({
                            "id": service.id,
                            "name": service.name,
                            "description": service.description,
                            "required_documents": service.required_documents
                        })
                else:
                    # Fallback to JSON config
                    services = selected_category.get("services", [])

                # Update conversation context
                conversation.context_data.update({
                    "selected_category": selected_category,
                    "services": services
                })
                conversation.current_step = "service_selection"
                self.db.commit()

                return self.whatsapp.format_sub_service_list(services, selected_category["name"])
            else:
                return "❌ Invalid choice. Please select a valid number from the list."

        except ValueError:
            return "❌ Please enter a valid number."
    
    def handle_service_selection(self, conversation: Conversation, user_input: str) -> str:
        """Handle specific service selection"""
        try:
            choice = int(user_input.strip()) - 1
            services = conversation.context_data.get("services", [])

            if 0 <= choice < len(services):
                selected_service = services[choice]

                # Create application record with proper service_id
                service_id = selected_service.get("id", None)
                application = Application(
                    user_id=conversation.user_id,
                    service_id=service_id,
                    status="pending"
                )
                self.db.add(application)
                self.db.commit()
                self.db.refresh(application)

                # Update conversation context
                conversation.context_data.update({
                    "selected_service": selected_service,
                    "application_id": application.id,
                    "required_documents": selected_service["required_documents"].copy(),
                    "uploaded_documents": []
                })
                conversation.current_step = "document_upload"
                conversation.current_application_id = application.id
                self.db.commit()

                return self.whatsapp.format_document_requirements(
                    selected_service["name"],
                    selected_service["required_documents"]
                )
            else:
                return "❌ Invalid choice. Please select a valid number from the list."

        except ValueError:
            return "❌ Please enter a valid number."
    
    def handle_document_upload(self, conversation: Conversation, media_url: str = None) -> str:
        """Handle document upload with OCR processing"""
        if not media_url:
            return "📩 Please upload a document (image or PDF file)."

        required_docs = conversation.context_data.get("required_documents", [])
        uploaded_docs = conversation.context_data.get("uploaded_documents", [])

        if not required_docs:
            return self.handle_confirmation_request(conversation)

        # Get the next required document
        next_doc = required_docs.pop(0)
        application_id = conversation.context_data.get("application_id")

        try:
            # Download and save the document
            import asyncio
            import httpx
            import tempfile
            import os

            # Download file from media URL
            async def download_and_process():
                async with httpx.AsyncClient() as client:
                    response = await client.get(media_url)
                    if response.status_code == 200:
                        # Create temporary file
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                            temp_file.write(response.content)
                            temp_path = temp_file.name

                        # Save document using document service
                        document = await self.document_service.save_file(
                            response.content,
                            f"{next_doc}.jpg",
                            application_id,
                            next_doc
                        )

                        # Clean up temp file
                        os.unlink(temp_path)

                        return document
                    return None

            # Run async download
            document = asyncio.run(download_and_process())

            if document:
                # Add to uploaded documents with OCR info
                doc_info = {
                    "type": next_doc,
                    "media_url": media_url,
                    "uploaded_at": str(conversation.updated_at),
                    "file_path": document.file_path,
                    "ocr_processed": hasattr(document, 'ocr_result') and document.ocr_result is not None
                }

                if hasattr(document, 'ocr_result') and document.ocr_result:
                    doc_info["ocr_confidence"] = document.ocr_result.get('average_confidence', 0)

                uploaded_docs.append(doc_info)

                # Update conversation context
                conversation.context_data.update({
                    "required_documents": required_docs,
                    "uploaded_documents": uploaded_docs
                })

                if not required_docs:
                    conversation.current_step = "confirmation"

                self.db.commit()

                # Format response with OCR info
                response = self.whatsapp_api.format_document_received(next_doc, required_docs)

                # Add OCR results if available
                if hasattr(document, 'ocr_result') and document.ocr_result:
                    ocr_info = self.whatsapp_api.format_ocr_results(document.ocr_result)
                    response += "\n\n" + ocr_info

                return response
            else:
                return "❌ Failed to download document. Please try uploading again."

        except Exception as e:
            logger.error(f"Error processing document upload: {str(e)}")
            return "❌ Error processing document. Please try uploading again."
    
    def handle_confirmation_request(self, conversation: Conversation) -> str:
        """Handle final confirmation request"""
        conversation.current_step = "confirmation"
        self.db.commit()
        
        return ("🎉 All documents received!\n\n"
                "Do you want to submit your application now?\n\n"
                "Reply with:\n"
                "• *Yes* - Submit application\n"
                "• *No* - Cancel and start over")
    
    def handle_confirmation(self, conversation: Conversation, user_input: str) -> str:
        """Handle final confirmation"""
        user_input = user_input.lower().strip()
        
        if user_input in ['yes', 'y', '1']:
            # Submit application
            application_id = conversation.context_data.get("application_id")
            if application_id:
                application = self.db.query(Application).filter(Application.id == application_id).first()
                if application:
                    application.status = "submitted"
                    self.db.commit()
            
            # Reset conversation
            conversation.is_active = False
            self.db.commit()
            
            return self.whatsapp.format_submission_confirmation(application_id or 0)
            
        elif user_input in ['no', 'n', '0']:
            # Cancel and restart
            conversation.current_step = "greeting"
            conversation.context_data = {}
            conversation.current_application_id = None
            self.db.commit()
            
            return "❌ Application cancelled.\n\n" + self.handle_greeting(conversation)
        else:
            return "Please reply with *Yes* to submit or *No* to cancel."
    
    def process_message(self, phone_number: str, message_content: str, media_url: str = None, twilio_sid: str = None) -> str:
        """Main method to process incoming messages"""
        try:
            # Get or create user and conversation
            user = self.get_or_create_user(phone_number)
            conversation = self.get_or_create_conversation(user)
            
            # Save incoming message
            self.save_message(conversation, "incoming", message_content, media_url, twilio_sid)
            
            # Handle restart command
            if message_content.lower().strip() in ['start', 'restart', 'begin']:
                conversation.current_step = "greeting"
                conversation.context_data = {}
                conversation.current_application_id = None
                self.db.commit()
            
            # Process based on current step
            response = ""
            if conversation.current_step == "greeting":
                response = self.handle_greeting(conversation)
            elif conversation.current_step == "category_selection":
                response = self.handle_category_selection(conversation, message_content)
            elif conversation.current_step == "service_selection":
                response = self.handle_service_selection(conversation, message_content)
            elif conversation.current_step == "document_upload":
                response = self.handle_document_upload(conversation, media_url)
            elif conversation.current_step == "confirmation":
                response = self.handle_confirmation(conversation, message_content)
            else:
                response = "I'm sorry, something went wrong. Type *start* to begin again."
            
            # Save outgoing message
            self.save_message(conversation, "outgoing", response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return "I'm sorry, there was an error processing your request. Please try again or type *start* to begin."
