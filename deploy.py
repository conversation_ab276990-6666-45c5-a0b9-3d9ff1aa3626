#!/usr/bin/env python3
"""
Deployment script for WhatsApp Government Services Chatbot
Supports multiple deployment environments: local, staging, production
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

class ChatbotDeployer:
    def __init__(self, environment="local"):
        self.environment = environment
        self.project_root = Path(__file__).parent
        self.deploy_configs = {
            "local": {
                "host": "0.0.0.0",
                "port": 8000,
                "workers": 1,
                "database_url": "sqlite:///./chatbot.db",
                "debug": True
            },
            "staging": {
                "host": "0.0.0.0", 
                "port": 8000,
                "workers": 2,
                "database_url": "postgresql://user:pass@localhost/chatbot_staging",
                "debug": True
            },
            "production": {
                "host": "0.0.0.0",
                "port": 8000,
                "workers": 4,
                "database_url": "postgresql://user:pass@localhost/chatbot_prod",
                "debug": False
            }
        }
    
    def check_requirements(self):
        """Check if all requirements are met"""
        print("🔍 Checking deployment requirements...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        
        # Check if required files exist
        required_files = [
            "main.py", "requirements.txt", "config.py",
            "models/database.py", "routes/webhook.py"
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                print(f"❌ Required file missing: {file_path}")
                return False
        
        print("✅ All requirements met")
        return True
    
    def install_dependencies(self):
        """Install Python dependencies"""
        print("📦 Installing dependencies...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True, cwd=self.project_root)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    def setup_environment(self):
        """Setup environment configuration"""
        print(f"⚙️ Setting up {self.environment} environment...")
        
        config = self.deploy_configs[self.environment]
        env_file = self.project_root / ".env"
        
        # Create .env file if it doesn't exist
        if not env_file.exists():
            shutil.copy(self.project_root / ".env.example", env_file)
        
        # Update environment variables
        env_vars = {
            "DEBUG": str(config["debug"]).lower(),
            "HOST": config["host"],
            "PORT": str(config["port"]),
            "DATABASE_URL": config["database_url"]
        }
        
        # Read existing .env
        env_content = {}
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        env_content[key] = value
        
        # Update with deployment config
        env_content.update(env_vars)
        
        # Write back to .env
        with open(env_file, 'w') as f:
            for key, value in env_content.items():
                f.write(f"{key}={value}\n")
        
        print("✅ Environment configured")
        return True
    
    def setup_database(self):
        """Setup database"""
        print("🗄️ Setting up database...")
        try:
            subprocess.run([
                sys.executable, "setup.py"
            ], check=True, cwd=self.project_root)
            print("✅ Database setup completed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Database setup failed: {e}")
            return False
    
    def create_systemd_service(self):
        """Create systemd service file for production"""
        if self.environment != "production":
            return True
        
        print("🔧 Creating systemd service...")
        
        service_content = f"""[Unit]
Description=WhatsApp Government Services Chatbot
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory={self.project_root}
Environment=PATH={self.project_root}/venv/bin
ExecStart={sys.executable} -m uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_file = Path("/etc/systemd/system/whatsapp-chatbot.service")
        try:
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            subprocess.run(["systemctl", "daemon-reload"], check=True)
            subprocess.run(["systemctl", "enable", "whatsapp-chatbot"], check=True)
            
            print("✅ Systemd service created")
            return True
        except (PermissionError, subprocess.CalledProcessError) as e:
            print(f"⚠️ Could not create systemd service: {e}")
            print("Run with sudo for production deployment")
            return False
    
    def create_nginx_config(self):
        """Create nginx configuration for production"""
        if self.environment != "production":
            return True
        
        print("🌐 Creating nginx configuration...")
        
        nginx_config = """server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /uploads/ {
        alias /path/to/your/project/uploads/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
"""
        
        config_file = Path("/etc/nginx/sites-available/whatsapp-chatbot")
        try:
            with open(config_file, 'w') as f:
                f.write(nginx_config)
            
            # Create symlink
            symlink = Path("/etc/nginx/sites-enabled/whatsapp-chatbot")
            if not symlink.exists():
                symlink.symlink_to(config_file)
            
            print("✅ Nginx configuration created")
            print("⚠️ Remember to:")
            print("  - Update server_name with your domain")
            print("  - Update upload path")
            print("  - Test nginx config: nginx -t")
            print("  - Reload nginx: systemctl reload nginx")
            return True
        except (PermissionError, subprocess.CalledProcessError) as e:
            print(f"⚠️ Could not create nginx config: {e}")
            return False
    
    def start_application(self):
        """Start the application"""
        print("🚀 Starting application...")
        
        config = self.deploy_configs[self.environment]
        
        if self.environment == "production":
            try:
                subprocess.run(["systemctl", "start", "whatsapp-chatbot"], check=True)
                subprocess.run(["systemctl", "status", "whatsapp-chatbot"], check=True)
                print("✅ Production service started")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to start production service: {e}")
                return False
        else:
            # Development/staging - run directly
            cmd = [
                sys.executable, "-m", "uvicorn", "main:app",
                "--host", config["host"],
                "--port", str(config["port"]),
                "--workers", str(config["workers"])
            ]
            
            if config["debug"]:
                cmd.append("--reload")
            
            print(f"Starting with command: {' '.join(cmd)}")
            print(f"🌐 Application will be available at: http://{config['host']}:{config['port']}")
            
            try:
                subprocess.run(cmd, cwd=self.project_root)
                return True
            except KeyboardInterrupt:
                print("\n👋 Application stopped")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to start application: {e}")
                return False
    
    def deploy(self):
        """Main deployment function"""
        print(f"🚀 Deploying WhatsApp Chatbot to {self.environment}")
        print("=" * 50)
        
        steps = [
            ("Check Requirements", self.check_requirements),
            ("Install Dependencies", self.install_dependencies),
            ("Setup Environment", self.setup_environment),
            ("Setup Database", self.setup_database),
        ]
        
        if self.environment == "production":
            steps.extend([
                ("Create Systemd Service", self.create_systemd_service),
                ("Create Nginx Config", self.create_nginx_config),
            ])
        
        steps.append(("Start Application", self.start_application))
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            if not step_func():
                print(f"❌ Deployment failed at: {step_name}")
                return False
        
        print("\n" + "=" * 50)
        print("🎉 Deployment completed successfully!")
        
        if self.environment == "production":
            print("\n📋 Post-deployment checklist:")
            print("  ✅ Update Twilio webhook URL")
            print("  ✅ Configure SSL certificate")
            print("  ✅ Set up monitoring")
            print("  ✅ Configure backups")
        
        return True

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Deploy WhatsApp Government Services Chatbot")
    parser.add_argument(
        "environment",
        choices=["local", "staging", "production"],
        default="local",
        nargs="?",
        help="Deployment environment"
    )
    
    args = parser.parse_args()
    
    deployer = ChatbotDeployer(args.environment)
    success = deployer.deploy()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
