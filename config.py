import os
from typing import List
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # WhatsApp API Configuration
    whatsapp_api_key: str = os.getenv("WHATSAPP_API_KEY", "67fd47ab51b34699a1822c669b5d3f99")
    whatsapp_phone_number: str = os.getenv("WHATSAPP_PHONE_NUMBER", "**********")
    whatsapp_api_url: str = os.getenv("WHATSAPP_API_URL", "https://api.whatsapp.com/send")

    # Twilio Configuration (Backup)
    twilio_account_sid: str = os.getenv("TWILIO_ACCOUNT_SID", "")
    twilio_auth_token: str = os.getenv("TWILIO_AUTH_TOKEN", "")
    twilio_whatsapp_number: str = os.getenv("TWILIO_WHATSAPP_NUMBER", "")
    
    # FastAPI Configuration
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-change-this")
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"
    host: str = os.getenv("HOST", "0.0.0.0")
    port: int = int(os.getenv("PORT", "8000"))
    
    # Database Configuration
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./chatbot.db")
    
    # File Upload Configuration
    upload_dir: str = os.getenv("UPLOAD_DIR", r"C:\Users\<USER>\Desktop\New folder")
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "********"))  # 10MB
    allowed_extensions: List[str] = os.getenv("ALLOWED_EXTENSIONS", "pdf,jpg,jpeg,png,doc,docx").split(",")

    # OCR Configuration
    ocr_enabled: bool = os.getenv("OCR_ENABLED", "true").lower() == "true"
    ocr_output_dir: str = os.getenv("OCR_OUTPUT_DIR", r"C:\Users\<USER>\Desktop\New folder")
    ocr_language: str = os.getenv("OCR_LANGUAGE", "eng+hin")
    tesseract_path: str = os.getenv("TESSERACT_PATH", r"C:\Program Files\Tesseract-OCR\tesseract.exe")
    
    # Admin Configuration
    admin_username: str = os.getenv("ADMIN_USERNAME", "admin")
    admin_password: str = os.getenv("ADMIN_PASSWORD", "admin123")

    # External Database Configuration for OCR Storage
    external_db_host: str = os.getenv("EXTERNAL_DB_HOST", "*************")
    external_db_name: str = os.getenv("EXTERNAL_DB_NAME", "kir_cec")
    external_db_user: str = os.getenv("EXTERNAL_DB_USER", "bivices")
    external_db_password: str = os.getenv("EXTERNAL_DB_PASSWORD", "26")
    external_db_port: int = int(os.getenv("EXTERNAL_DB_PORT", "3306"))
    


settings = Settings()

# Create upload directory if it doesn't exist
os.makedirs(settings.upload_dir, exist_ok=True)
