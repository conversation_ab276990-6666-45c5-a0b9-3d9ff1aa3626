from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import logging
import os

# Import routes
from routes.webhook import router as webhook_router
from routes.whatsapp_webhook import router as whatsapp_webhook_router
from routes.admin import router as admin_router

# Import models and database
from models.database import create_tables
from config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="WhatsApp Government Services Chatbot",
    description="A chatbot for government services via WhatsApp",
    version="1.0.0",
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create upload directory
os.makedirs(settings.upload_dir, exist_ok=True)

# Mount static files for uploaded documents (for admin access)
if os.path.exists(settings.upload_dir):
    app.mount("/uploads", StaticFiles(directory=settings.upload_dir), name="uploads")

# Include routers
app.include_router(webhook_router, prefix="/webhook", tags=["webhook"])
app.include_router(whatsapp_webhook_router, prefix="/webhook", tags=["whatsapp"])
app.include_router(admin_router, tags=["admin"])

@app.on_event("startup")
async def startup_event():
    """Initialize database and load initial data"""
    logger.info("Starting WhatsApp Chatbot application...")
    
    # Create database tables
    create_tables()
    logger.info("Database tables created/verified")
    
    # Load services from configuration
    try:
        from routes.admin import load_services_from_config
        from models.database import SessionLocal
        
        db = SessionLocal()
        # This will be called via admin endpoint
        logger.info("Application startup completed")
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with basic information"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WhatsApp Government Services Chatbot</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .status { background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
            .code { font-family: monospace; background: #f0f0f0; padding: 2px 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 WhatsApp Government Services Chatbot</h1>
            
            <div class="status">
                <h3>✅ Service Status: Active</h3>
                <p>The chatbot is running and ready to handle WhatsApp messages.</p>
            </div>
            
            <h2>📋 Available Endpoints</h2>
            
            <div class="endpoint">
                <h4>WhatsApp Webhook</h4>
                <p><strong>POST</strong> <span class="code">/webhook/whatsapp</span></p>
                <p>Handles incoming WhatsApp messages from Twilio</p>
            </div>
            
            <div class="endpoint">
                <h4>Admin Dashboard</h4>
                <p><strong>GET</strong> <span class="code">/admin/stats</span> - Get dashboard statistics</p>
                <p><strong>GET</strong> <span class="code">/admin/users</span> - List all users</p>
                <p><strong>GET</strong> <span class="code">/admin/applications</span> - List all applications</p>
                <p><strong>POST</strong> <span class="code">/admin/services/load-from-config</span> - Load services from config</p>
            </div>
            
            <div class="endpoint">
                <h4>API Documentation</h4>
                <p><a href="/docs">📖 Interactive API Documentation (Swagger)</a></p>
                <p><a href="/redoc">📚 Alternative API Documentation (ReDoc)</a></p>
            </div>
            
            <h2>🚀 Getting Started</h2>
            <ol>
                <li>Configure your Twilio WhatsApp credentials in <span class="code">.env</span></li>
                <li>Load initial services: <span class="code">POST /admin/services/load-from-config</span></li>
                <li>Set up your Twilio webhook URL to point to <span class="code">/webhook/whatsapp</span></li>
                <li>Start chatting via WhatsApp!</li>
            </ol>
            
            <h2>💬 How to Use the Chatbot</h2>
            <ol>
                <li>Send "start" to begin</li>
                <li>Select a service category (e.g., Medical, Town Planning)</li>
                <li>Choose a specific service (e.g., Birth Certificate)</li>
                <li>Upload required documents one by one</li>
                <li>Confirm submission</li>
            </ol>
            
            <p><em>Built with FastAPI, SQLAlchemy, and Twilio WhatsApp API</em></p>
        </div>
    </body>
    </html>
    """
    return html_content

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "WhatsApp Government Services Chatbot",
        "version": "1.0.0"
    }

@app.get("/config")
async def get_config():
    """Get current configuration (non-sensitive data only)"""
    return {
        "debug": settings.debug,
        "upload_dir": settings.upload_dir,
        "max_file_size_mb": settings.max_file_size / (1024 * 1024),
        "allowed_extensions": settings.allowed_extensions,
        "twilio_configured": bool(settings.twilio_account_sid and settings.twilio_auth_token)
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
