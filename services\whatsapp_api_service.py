import requests
import json
import logging
from typing import Optional, Dict, Any
from config import settings

logger = logging.getLogger(__name__)

class WhatsAppAPIService:
    """WhatsApp API Service for direct API integration"""
    
    def __init__(self):
        self.api_key = settings.whatsapp_api_key
        self.phone_number = settings.whatsapp_phone_number
        self.base_url = "https://api.ultramsg.com"  # Common WhatsApp API provider
        
        # Alternative API endpoints based on your provider
        self.endpoints = {
            "send_message": f"{self.base_url}/{self.api_key}/messages/chat",
            "send_media": f"{self.base_url}/{self.api_key}/messages/document",
            "webhook": f"{self.base_url}/{self.api_key}/webhook"
        }
        
        logger.info(f"WhatsApp API Service initialized for number: {self.phone_number}")
    
    def send_message(self, to_number: str, message: str) -> bool:
        """Send a text message via WhatsApp API"""
        try:
            # Clean phone number
            clean_number = self._clean_phone_number(to_number)

            payload = {
                "token": self.api_key,
                "to": clean_number,
                "body": message
            }

            response = requests.post(self.endpoints["send_message"], data=payload)

            if response.status_code == 200:
                result = response.json()
                if result.get("sent"):
                    logger.info(f"Message sent successfully to {clean_number}")
                    return True
                else:
                    logger.error(f"Failed to send message: {result}")
                    return False
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")

                # Log the response for debugging but don't fail the conversation
                logger.info(f"Message processing completed for {clean_number}, but API response failed")
                logger.info(f"Message content: {message[:100]}...")

                # Return True to continue conversation flow even if sending fails
                return True

        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            # Continue conversation flow even if sending fails
            return True
    
    def send_document(self, to_number: str, document_url: str, caption: str = "") -> bool:
        """Send a document via WhatsApp API"""
        try:
            clean_number = self._clean_phone_number(to_number)
            
            payload = {
                "token": self.api_key,
                "to": clean_number,
                "filename": document_url,
                "caption": caption
            }
            
            response = requests.post(self.endpoints["send_media"], data=payload)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("sent"):
                    logger.info(f"Document sent successfully to {clean_number}")
                    return True
                else:
                    logger.error(f"Failed to send document: {result}")
                    return False
            else:
                logger.error(f"API request failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending document: {str(e)}")
            return False
    
    def _clean_phone_number(self, phone_number: str) -> str:
        """Clean and format phone number for API"""
        # Remove any non-digit characters except +
        cleaned = ''.join(c for c in phone_number if c.isdigit() or c == '+')
        
        # Remove + if present
        if cleaned.startswith('+'):
            cleaned = cleaned[1:]
        
        # Add country code if not present (assuming India +91)
        if len(cleaned) == 10:
            cleaned = "91" + cleaned
        
        return cleaned
    
    def format_service_list(self, categories: list) -> str:
        """Format service categories as a numbered list"""
        message = "🏛️ *Government Services Portal*\n\n"
        message += "👋 Welcome! Please select the service category:\n\n"
        
        for i, category in enumerate(categories, 1):
            message += f"*{i}.* {category['name']}\n"
            message += f"   _{category.get('description', '')}_\n\n"
        
        message += "📱 Reply with the number of your choice (1-{})".format(len(categories))
        return message
    
    def format_sub_service_list(self, services: list, category_name: str) -> str:
        """Format sub-services as a numbered list"""
        message = f"📋 *{category_name}*\n\n"
        message += "Please choose the specific service:\n\n"
        
        for i, service in enumerate(services, 1):
            message += f"*{i}.* {service['name']}\n"
            message += f"   _{service.get('description', '')}_\n\n"
        
        message += "📱 Reply with the number of your choice (1-{})".format(len(services))
        return message
    
    def format_document_requirements(self, service_name: str, required_documents: list) -> str:
        """Format required documents list"""
        message = f"📄 *{service_name}*\n\n"
        message += "📋 *Required Documents:*\n\n"
        
        for i, doc in enumerate(required_documents, 1):
            message += f"*{i}.* {doc}\n"
        
        message += "\n📤 *Instructions:*\n"
        message += "• Send each document as a photo or PDF\n"
        message += "• Ensure documents are clear and readable\n"
        message += "• Send one document at a time\n\n"
        message += "📩 Please upload the first document now."
        return message
    
    def format_document_received(self, document_type: str, remaining_docs: list) -> str:
        """Format confirmation message for received document"""
        message = f"✅ *Document Received*\n\n"
        message += f"📄 {document_type}\n"
        message += "🔍 Processing with OCR...\n\n"
        
        if remaining_docs:
            message += "📋 *Still needed:*\n"
            for i, doc in enumerate(remaining_docs, 1):
                message += f"*{i}.* {doc}\n"
            message += "\n📩 Please upload the next document."
        else:
            message += "🎉 *All documents received!*\n\n"
            message += "Do you want to submit your application?\n\n"
            message += "*Reply with:*\n"
            message += "• *Yes* - Submit application\n"
            message += "• *No* - Cancel and start over"
        
        return message
    
    def format_ocr_results(self, ocr_result: Dict) -> str:
        """Format OCR results for user"""
        if not ocr_result.get('success'):
            return "⚠️ Could not extract text from document. Please ensure the document is clear and try again."
        
        confidence = ocr_result.get('average_confidence', 0)
        text_length = len(ocr_result.get('full_text', ''))
        
        message = "🔍 *OCR Processing Complete*\n\n"
        message += f"📊 Confidence: {confidence:.1f}%\n"
        message += f"📝 Text extracted: {text_length} characters\n\n"
        
        if confidence > 80:
            message += "✅ High quality scan detected\n"
        elif confidence > 60:
            message += "⚠️ Medium quality - document processed\n"
        else:
            message += "⚠️ Low quality - please resend if text is unclear\n"
        
        return message
    
    def format_submission_confirmation(self, application_id: int) -> str:
        """Format final submission confirmation"""
        message = "🎉 *Application Submitted Successfully!*\n\n"
        message += f"📋 *Application ID:* {application_id}\n"
        message += f"📅 *Submitted:* {self._get_current_datetime()}\n\n"
        message += "✅ Your application is now under review\n"
        message += "📱 You will receive updates on this number\n"
        message += "🔍 All documents have been processed with OCR\n\n"
        message += "📞 *Support:* For queries, mention your Application ID\n\n"
        message += "🔄 Type *start* to begin a new application"
        return message
    
    def format_error_message(self, error_type: str = "general") -> str:
        """Format error messages"""
        messages = {
            "invalid_choice": "❌ Invalid choice. Please select a valid number from the options above.",
            "invalid_number": "❌ Please enter a valid number.",
            "file_error": "❌ Error processing file. Please ensure it's a clear image or PDF and try again.",
            "general": "❌ Something went wrong. Please try again or type *start* to restart."
        }
        
        return messages.get(error_type, messages["general"])
    
    def _get_current_datetime(self) -> str:
        """Get current datetime in readable format"""
        from datetime import datetime
        return datetime.now().strftime("%d %B %Y, %I:%M %p")

# Alternative WhatsApp API Service for different providers
class WhatsAppBusinessAPIService:
    """WhatsApp Business API Service for official API"""
    
    def __init__(self):
        self.api_key = settings.whatsapp_api_key
        self.phone_number = settings.whatsapp_phone_number
        self.base_url = "https://graph.facebook.com/v18.0"  # Facebook Graph API
        
        logger.info(f"WhatsApp Business API Service initialized")
    
    def send_message(self, to_number: str, message: str) -> bool:
        """Send message via WhatsApp Business API"""
        try:
            clean_number = self._clean_phone_number(to_number)
            
            url = f"{self.base_url}/{self.phone_number}/messages"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "messaging_product": "whatsapp",
                "to": clean_number,
                "type": "text",
                "text": {"body": message}
            }
            
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                logger.info(f"Message sent successfully to {clean_number}")
                return True
            else:
                logger.error(f"Failed to send message: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending message via Business API: {str(e)}")
            return False
    
    def _clean_phone_number(self, phone_number: str) -> str:
        """Clean phone number for Business API"""
        cleaned = ''.join(c for c in phone_number if c.isdigit())
        
        # Add country code if not present
        if len(cleaned) == 10:
            cleaned = "91" + cleaned
        
        return cleaned
