version: '3.8'

services:
  # WhatsApp Chatbot Application
  chatbot:
    build: .
    container_name: whatsapp-chatbot
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - DATABASE_URL=****************************************************/chatbot_db
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_WHATSAPP_NUMBER=${TWILIO_WHATSAPP_NUMBER}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - chatbot-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chatbot-postgres
    environment:
      - POSTGRES_DB=chatbot_db
      - POSTGRES_USER=chatbot_user
      - POSTGRES_PASSWORD=chatbot_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - chatbot-network

  # Redis for Caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: chatbot-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - chatbot-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: chatbot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./uploads:/var/www/uploads:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - chatbot
    restart: unless-stopped
    networks:
      - chatbot-network

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: chatbot-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - chatbot-network

  # Grafana for Dashboards (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: chatbot-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    restart: unless-stopped
    networks:
      - chatbot-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  chatbot-network:
    driver: bridge
